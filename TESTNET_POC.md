# Scroll Sepolia Testnet POC - Cross-Domain Message Sender Spoofing

This directory contains a complete Proof of Concept (POC) to demonstrate the cross-domain message sender spoofing vulnerability on Scroll Sepolia testnet.

## 🎯 Objective

Prove that the `_from` parameter in `L2ScrollMessenger.relayMessage()` is user-controlled by:
1. Deploying an attacker contract on Sepolia L1
2. Sending malicious cross-domain messages
3. Observing how the live Scroll sequencer processes these messages
4. Confirming that the attacker's address becomes the `_from` parameter

## 📋 Prerequisites

1. **Foundry installed** with Scroll Sepolia RPC configured
2. **Private key** with Sepolia ETH (~0.01 ETH for gas fees)
3. **Scroll Sepolia ETH** for L2 operations (get from faucet)
4. **API keys** for Etherscan and Scrollscan (optional, for verification)

## 🔧 Setup

### 1. Environment Variables

Create a `.env` file:
```bash
PRIVATE_KEY=your_private_key_here
ETHERSCAN_API_KEY=your_etherscan_key (optional)
SCROLLSCAN_API_KEY=your_scrollscan_key (optional)
```

### 2. Get Testnet ETH

- **Sepolia ETH**: https://sepoliafaucet.com/
- **Scroll Sepolia ETH**: https://sepolia.scroll.io/faucet

## 🚀 Execution Steps

### Step 1: Deploy L1 Attacker Contract

Deploy the attacker contract on Sepolia L1:

```bash
forge script scripts/DeployL1Attacker.s.sol --rpc-url sepolia --broadcast --verify
```

**Expected Output:**
```
L1AttackerContract deployed at: 0x[ADDRESS]
Attacker address (will be _from in L2): 0x[SAME_ADDRESS]
```

**Save this address** - it will be the `_from` parameter in L2ScrollMessenger.relayMessage()!

### Step 2: Deploy L2 Test Token

Deploy a test token on Scroll Sepolia L2:

```bash
forge script scripts/DeployL2Token.s.sol --rpc-url scroll_sepolia --broadcast --verify
```

**Expected Output:**
```
TestL2Token deployed at: 0x[L2_TOKEN_ADDRESS]
```

### Step 3: Update Attack Script

Edit `scripts/ExecuteAttack.s.sol` and set the deployed addresses:

```solidity
address constant L1_ATTACKER_CONTRACT = 0x[YOUR_L1_ATTACKER_ADDRESS];
address constant L2_TEST_TOKEN = 0x[YOUR_L2_TOKEN_ADDRESS];
```

### Step 4: Fund Attacker Contract

Send some Sepolia ETH to the attacker contract for message fees:

```bash
cast send 0x[L1_ATTACKER_ADDRESS] --value 0.01ether --rpc-url sepolia --private-key $PRIVATE_KEY
```

### Step 5: Execute the Attack

Run the vulnerability POC:

```bash
forge script scripts/ExecuteAttack.s.sol --rpc-url sepolia --broadcast
```

**This will send two malicious cross-domain messages:**
1. **Token Mapping Hijack**: Updates L2 token mapping to point to attacker contract
2. **Unlimited Minting**: Mints 1M tokens using the hijacked mapping

### Step 6: Monitor Results

Wait 5-10 minutes for sequencer processing, then check results:

```bash
# Update monitoring script with your addresses first
forge script scripts/MonitorAttack.s.sol --rpc-url scroll_sepolia
```

## 🔍 What to Look For

### 1. L1 Transactions (Sepolia Etherscan)

- Look for transactions to `L1ScrollMessenger` contract
- Verify `sendMessage` calls from your attacker contract
- Note the transaction hashes for cross-reference

### 2. L2 Transactions (Scroll Sepolia Explorer)

**Critical Evidence:**
- Find `L2ScrollMessenger.relayMessage()` transactions
- **Verify `_from` parameter equals your attacker contract address**
- Confirm `xDomainMessageSender` is set to attacker address
- Check that gateway functions are called successfully

### 3. Token State Changes

- **Token mapping hijacked**: `counterpart` points to attacker contract
- **Tokens minted**: Recipient balance increased by 1M tokens
- **Total supply increased**: Proves unlimited minting worked

## 📊 Expected Results

### ✅ If Vulnerability Exists (Our Claim)

1. **L2ScrollMessenger.relayMessage()** receives `_from = [ATTACKER_CONTRACT_ADDRESS]`
2. **xDomainMessageSender** is set to attacker address
3. **Gateway access controls bypassed** (onlyCallByCounterpart passes)
4. **Token mapping hijacked** to attacker contract
5. **1M tokens minted** to recipient address

### ❌ If Company is Correct

1. **L2ScrollMessenger.relayMessage()** receives `_from = [L1_SCROLL_MESSENGER_ADDRESS]`
2. **xDomainMessageSender** is set to L1ScrollMessenger
3. **Gateway access controls work** (onlyCallByCounterpart fails)
4. **No token mapping changes**
5. **No tokens minted**

## 🎯 Key Addresses

### Scroll Sepolia Mainnet Addresses
- **L1ScrollMessenger**: `******************************************`
- **L2ScrollMessenger**: `******************************************`
- **L2CustomERC20Gateway**: `******************************************`

### Your Deployed Addresses
- **L1AttackerContract**: `0x[SET_AFTER_DEPLOYMENT]`
- **TestL2Token**: `0x[SET_AFTER_DEPLOYMENT]`

## 🔗 Useful Links

- **Sepolia Etherscan**: https://sepolia.etherscan.io/
- **Scroll Sepolia Explorer**: https://sepolia.scrollscan.com/
- **Scroll Sepolia Bridge**: https://sepolia.scroll.io/bridge
- **Scroll Sepolia Faucet**: https://sepolia.scroll.io/faucet

## 📝 Documentation

This POC will provide definitive proof of whether:
1. The `_from` parameter in `L2ScrollMessenger.relayMessage()` is user-controlled
2. Attackers can spoof cross-domain message senders
3. The vulnerability enables complete bridge compromise
4. The company's assessment is correct or incorrect

**The results will be visible on-chain and verifiable by anyone.**
