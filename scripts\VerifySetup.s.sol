// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";

/**
 * @title VerifySetup
 * @notice Verify our entire setup is correct
 */
contract VerifySetup is Script {
    
    // Our addresses
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
    address constant L1_MESSAGE_QUEUE = 0xA0673eC0A48aa924f067F1274EcD281A10c5f19F;
    address constant DEPLOYER = 0xe00a8a3c66071B44C3aBdFd947Eaa1eA1D70dC6e;
    
    function run() external view {
        console.log("=== VERIFYING COMPLETE SETUP ===");
        
        // 1. Check if L1ScrollMessenger exists and is correct
        console.log("\n=== 1. L1SCROLLMESSENGER VERIFICATION ===");
        console.log("Address:", L1_SCROLL_MESSENGER);
        
        uint256 codeSize;
        assembly {
            codeSize := extcodesize(L1_SCROLL_MESSENGER)
        }
        console.log("Code size:", codeSize);
        
        if (codeSize == 0) {
            console.log("❌ ERROR: L1ScrollMessenger has no code!");
            return;
        } else {
            console.log("✅ L1ScrollMessenger exists");
        }
        
        // 2. Check counterpart
        try this.getCounterpart() returns (address counterpart) {
            console.log("Counterpart (should be L2ScrollMessenger):", counterpart);
            if (counterpart == L2_SCROLL_MESSENGER) {
                console.log("✅ Counterpart matches our L2ScrollMessenger");
            } else {
                console.log("❌ Counterpart mismatch!");
                console.log("Expected:", L2_SCROLL_MESSENGER);
                console.log("Actual:", counterpart);
            }
        } catch {
            console.log("❌ Could not get counterpart");
        }
        
        // 3. Check message queue
        try this.getMessageQueue() returns (address messageQueue) {
            console.log("Message Queue:", messageQueue);
            if (messageQueue == L1_MESSAGE_QUEUE) {
                console.log("✅ Message queue matches");
            } else {
                console.log("❌ Message queue mismatch!");
                console.log("Expected:", L1_MESSAGE_QUEUE);
                console.log("Actual:", messageQueue);
            }
        } catch {
            console.log("❌ Could not get message queue");
        }
        
        // 4. Check if paused
        try this.isPaused() returns (bool paused) {
            console.log("Is Paused:", paused);
            if (paused) {
                console.log("❌ L1ScrollMessenger is PAUSED! This explains the issue!");
            } else {
                console.log("✅ L1ScrollMessenger is not paused");
            }
        } catch {
            console.log("Could not check paused status");
        }
        
        // 5. Check recent activity on message queue
        console.log("\n=== 2. MESSAGE QUEUE ACTIVITY ===");
        try this.getNextMessageIndex() returns (uint256 nextIndex) {
            console.log("Next message index:", nextIndex);
            console.log("Our messages: 1067113, 1067114");
            if (nextIndex > 1067114) {
                console.log("✅ Queue has progressed past our messages");
            } else {
                console.log("⏳ Queue hasn't reached our messages yet");
            }
        } catch {
            console.log("❌ Could not check message queue index");
        }
        
        // 6. Check our transaction history
        console.log("\n=== 3. OUR TRANSACTIONS ===");
        console.log("Deployer address:", DEPLOYER);
        console.log("Check these transactions manually:");
        console.log("- 0x2081e1dbb362cdb3352fb12147d271a2a44bc84b400636f9a981a8573285ea61");
        console.log("- 0x098bed19f64e1dcff57fad2d800b152e9d6d124cf7d4c4240c5f62c29061f79f");
        
        console.log("\n=== 4. ALTERNATIVE EXPLANATION ===");
        console.log("Possible reasons for no L2 activity:");
        console.log("1. L1ScrollMessenger is paused");
        console.log("2. Wrong L1ScrollMessenger address");
        console.log("3. Sequencer is down/delayed");
        console.log("4. Our messages are being filtered/rejected");
        console.log("5. We're looking at wrong L2 address");
        
        console.log("\n=== 5. VERIFICATION STEPS ===");
        console.log("Manual checks to perform:");
        console.log("1. Verify L1 transactions actually called sendMessage");
        console.log("2. Check if events were emitted correctly");
        console.log("3. Verify message queue received the messages");
        console.log("4. Check Scroll bridge status page");
        console.log("5. Look for any error messages in L1 transactions");
    }
    
    function getCounterpart() external view returns (address) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("counterpart()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (address));
        }
        revert("Failed to get counterpart");
    }
    
    function getMessageQueue() external view returns (address) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("messageQueue()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (address));
        }
        revert("Failed to get message queue");
    }
    
    function isPaused() external view returns (bool) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("paused()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (bool));
        }
        revert("Failed to check paused status");
    }
    
    function getNextMessageIndex() external view returns (uint256) {
        (bool success, bytes memory data) = L1_MESSAGE_QUEUE.staticcall(
            abi.encodeWithSignature("nextCrossDomainMessageIndex()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (uint256));
        }
        revert("Failed to get next message index");
    }
}
