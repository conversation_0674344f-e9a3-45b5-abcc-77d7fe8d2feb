# Gateway Contract Mapping for Security Audit

## L2 to L1 Gateway Contract Pairs

| L2 Contract | L1 Counterpart |
|-------------|----------------|
| L2ETHGateway | L1ETHGateway |
| L2StandardERC20Gateway | L1StandardERC20Gateway |
| L2CustomERC20Gateway | L1CustomERC20Gateway |
| L2ERC721Gateway | L1ERC721Gateway |
| L2ERC1155Gateway | L1ERC1155Gateway |
| L2USDCGateway | L1USDCGateway |
| L2GatewayRouter | L1GatewayRouter |

## Contract Files for Detailed Audit

### ETH Gateways
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/L2ETHGateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/L1ETHGateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/IL2ETHGateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/IL1ETHGateway.sol`

### ERC20 Gateways
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/L2ERC20Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/L1ERC20Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/IL2ERC20Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/IL1ERC20Gateway.sol`

### ERC721 Gateways
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/L2ERC721Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/L1ERC721Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/IL2ERC721Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/IL1ERC721Gateway.sol`

### ERC1155 Gateways
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/L2ERC1155Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/L1ERC1155Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/IL2ERC1155Gateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/IL1ERC1155Gateway.sol`

### USDC Gateways
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/usdc/L2USDCGateway.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/usdc/L1USDCGateway.sol`

### Gateway Routers
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/L2GatewayRouter.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/L1GatewayRouter.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/gateways/IL2GatewayRouter.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/gateways/IL1GatewayRouter.sol`

### Messenger Contracts (Critical for Cross-Contract Analysis)
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L2/L2ScrollMessenger.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/L1/L1ScrollMessenger.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/libraries/ScrollMessengerBase.sol`

### Base Libraries and Utilities
- `/home/<USER>/scroll-analysis/scroll-contracts/src/libraries/gateway/ScrollGatewayBase.sol`
- `/home/<USER>/scroll-analysis/scroll-contracts/src/libraries/gateway/IScrollGateway.sol`

## Attack Vectors to Verify
1. Administrative Function Hijacking
2. Gateway Routing Manipulation
3. Unlimited Token Minting
