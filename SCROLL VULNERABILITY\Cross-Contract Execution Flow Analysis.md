# Cross-Contract Execution Flow Analysis

## Attack Vector 1: Unlimited Token Minting

### Attack Flow

1. **L1 Initiation**
   - Attacker deploys a malicious contract on L1
   - Attacker calls `L1ScrollMessenger.sendMessage()` with the following parameters:
     - `_to`: Address of L2StandardERC20Gateway
     - `_value`: 0 (no ETH needed)
     - `_message`: Encoded call to `finalizeDepositERC20(address _l1Token, address _l2Token, address _from, address _to, uint256 _amount, bytes memory _data)`
     - `_gasLimit`: Sufficient gas to execute the call

2. **L2 Message Processing**
   - `L2ScrollMessenger.relayMessage()` is called by the L1ScrollMessenger (after address aliasing)
   - `L2ScrollMessenger._executeMessage()` is executed with:
     - `_from`: Attacker's L1 address
     - `_to`: L2StandardERC20Gateway address
     - `_value`: 0
     - `_message`: Encoded call to finalizeDepositERC20
     - `_xDomainCalldataHash`: Hash of the calldata

3. **Validation Bypass**
   - `_validateTargetAddress(_to)` only checks that `_to != address(this)`
   - Since L2StandardERC20Gateway is not the messenger itself, validation passes
   - `xDomainMessageSender` is set to attacker's L1 address

4. **Gateway Execution**
   - Low-level call to L2StandardERC20Gateway with the attacker's crafted message
   - `L2StandardERC20Gateway.finalizeDepositERC20()` is executed
   - `onlyCallByCounterpart` modifier checks:
     - `_msgSender() == messenger` ✓ (call is from L2ScrollMessenger)
     - `counterpart == IScrollMessenger(messenger).xDomainMessageSender()` ✓ (attacker set `_from` to L1StandardERC20Gateway address)
   - All validation checks pass

5. **Token Minting**
   - Token validation occurs:
     - `msg.value == 0` ✓
     - `_l1Token != address(0)` ✓
     - `_l2Token == expectedL2Token` ✓ (attacker calculated the correct L2 token address)
   - If first deposit for this token, L2 token is deployed
   - `IScrollERC20Upgradeable(_l2Token).mint(_to, _amount)` mints tokens to attacker-specified address
   - Attacker can specify arbitrary amount, potentially minting billions of tokens

6. **Impact**
   - Unlimited minting of any L2 token
   - Complete devaluation of bridged assets
   - Potential to drain liquidity pools by selling minted tokens

### Exact Parameters

```javascript
// Attacker's L1 contract code
function attack(address l2StandardERC20Gateway, address l1Token, address recipient, uint256 amount) external {
    // Calculate the expected L2 token address
    address l2Token = computeL2TokenAddress(l2StandardERC20Gateway, l1Token);
    
    // Craft the message to mint tokens
    bytes memory message = abi.encodeWithSelector(
        0x8431f5c1, // finalizeDepositERC20 selector
        l1Token,
        l2Token,
        0x1111111111111111111111111111111111111111, // fake _from address (could be L1StandardERC20Gateway)
        recipient,
        amount,
        abi.encode(false, new bytes(0)) // _data parameter
    );
    
    // Send the message to L2
    IL1ScrollMessenger(L1_SCROLL_MESSENGER).sendMessage(
        l2StandardERC20Gateway,
        0, // _value
        message,
        1000000 // _gasLimit
    );
}
```

## Attack Vector 2: Gateway Routing Manipulation

### Attack Flow

1. **L1 Initiation**
   - Attacker deploys a malicious contract on L1
   - Attacker calls `L1ScrollMessenger.sendMessage()` with the following parameters:
     - `_to`: Address of L2GatewayRouter
     - `_value`: 0 (no ETH needed)
     - `_message`: Encoded call to `setETHGateway(address _newEthGateway)` or `setDefaultERC20Gateway(address _newDefaultERC20Gateway)`
     - `_gasLimit`: Sufficient gas to execute the call

2. **L2 Message Processing**
   - `L2ScrollMessenger.relayMessage()` is called by the L1ScrollMessenger
   - `L2ScrollMessenger._executeMessage()` is executed with:
     - `_from`: Attacker's L1 address
     - `_to`: L2GatewayRouter address
     - `_value`: 0
     - `_message`: Encoded call to setETHGateway or setDefaultERC20Gateway
     - `_xDomainCalldataHash`: Hash of the calldata

3. **Validation Bypass**
   - `_validateTargetAddress(_to)` only checks that `_to != address(this)`
   - Since L2GatewayRouter is not the messenger itself, validation passes
   - `xDomainMessageSender` is set to attacker's L1 address

4. **Gateway Router Execution**
   - Low-level call to L2GatewayRouter with the attacker's crafted message
   - `L2GatewayRouter.setETHGateway()` or `setDefaultERC20Gateway()` is executed
   - `onlyOwner` modifier checks `_msgSender() == owner()`
   - Since `_msgSender()` returns the caller of the function (L2ScrollMessenger), not `xDomainMessageSender`, this check fails
   - **However**, if the attacker can somehow become the owner of L2GatewayRouter, or if there's a vulnerability in the ownership mechanism, this attack could succeed

5. **Impact (if successful)**
   - Redirection of all ETH or ERC20 withdrawals to attacker-controlled malicious gateway
   - Theft of all future withdrawals
   - Complete compromise of the bridge for the affected asset types

### Exact Parameters

```javascript
// Attacker's L1 contract code
function attack(address l2GatewayRouter, address maliciousGateway) external {
    // Craft the message to change the ETH gateway
    bytes memory message = abi.encodeWithSelector(
        0x5b8d0908, // setETHGateway selector
        maliciousGateway
    );
    
    // Send the message to L2
    IL1ScrollMessenger(L1_SCROLL_MESSENGER).sendMessage(
        l2GatewayRouter,
        0, // _value
        message,
        500000 // _gasLimit
    );
}
```

## Attack Vector 3: Unauthorized ETH Withdrawals

### Attack Flow

1. **L1 Initiation**
   - Attacker deploys a malicious contract on L1
   - Attacker calls `L1ScrollMessenger.sendMessage()` with the following parameters:
     - `_to`: Address of L2ETHGateway
     - `_value`: Amount of ETH to withdraw
     - `_message`: Encoded call to `finalizeDepositETH(address _from, address _to, uint256 _amount, bytes calldata _data)`
     - `_gasLimit`: Sufficient gas to execute the call

2. **L2 Message Processing**
   - `L2ScrollMessenger.relayMessage()` is called by the L1ScrollMessenger
   - `L2ScrollMessenger._executeMessage()` is executed with:
     - `_from`: Attacker's L1 address
     - `_to`: L2ETHGateway address
     - `_value`: Amount of ETH to withdraw
     - `_message`: Encoded call to finalizeDepositETH
     - `_xDomainCalldataHash`: Hash of the calldata

3. **Validation Bypass**
   - `_validateTargetAddress(_to)` only checks that `_to != address(this)`
   - Since L2ETHGateway is not the messenger itself, validation passes
   - `xDomainMessageSender` is set to attacker's L1 address

4. **Gateway Execution**
   - Low-level call to L2ETHGateway with the attacker's crafted message
   - `L2ETHGateway.finalizeDepositETH()` is executed
   - `onlyCallByCounterpart` modifier checks:
     - `_msgSender() == messenger` ✓ (call is from L2ScrollMessenger)
     - `counterpart == IScrollMessenger(messenger).xDomainMessageSender()` ✗ (attacker's address != L1ETHGateway)
   - This check fails because the attacker's address is not the L1ETHGateway address

5. **Conclusion**
   - This attack vector fails because the `onlyCallByCounterpart` modifier correctly validates that the cross-domain sender is the counterpart gateway
   - Even though the attacker can control `xDomainMessageSender`, they cannot set it to the L1ETHGateway address unless they control that address

### Revised Attack Vector: Impersonating the L1 Gateway

If the attacker could somehow set `_from` to the L1ETHGateway address, the attack could succeed:

1. **L1 Initiation (Modified)**
   - Attacker deploys a malicious contract on L1
   - Attacker calls `L1ScrollMessenger.sendMessage()` with:
     - `_from`: Set to L1ETHGateway address (if possible)
     - Other parameters as before

2. **Gateway Execution (Modified)**
   - `onlyCallByCounterpart` modifier checks:
     - `_msgSender() == messenger` ✓ (call is from L2ScrollMessenger)
     - `counterpart == IScrollMessenger(messenger).xDomainMessageSender()` ✓ (now passes because `_from` is L1ETHGateway)
   - All validation checks pass
   - ETH is transferred to attacker-specified address

3. **Impact (if successful)**
   - Unauthorized ETH transfers to attacker-controlled addresses
   - Potential to drain all ETH from the L2ETHGateway

### Key Finding

The security of the gateway contracts relies heavily on the assumption that `xDomainMessageSender` cannot be spoofed to be the counterpart gateway address. If an attacker can control this value (through the L2ScrollMessenger vulnerability or other means), they could bypass the `onlyCallByCounterpart` modifier and execute privileged functions.
