// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";

interface IL1ScrollMessenger {
    function sendMessage(
        address _to,
        uint256 _value,
        bytes memory _message,
        uint256 _gasLimit
    ) external payable;
}

contract TestL1Messenger is Script {
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== TESTING L1SCROLLMESSENGER DIRECTLY ===");
        console.log("Deployer:", deployer);
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("L2ScrollMessenger:", L2_SCROLL_MESSENGER);

        // Check deployer balance
        uint256 balance = deployer.balance;
        console.log("Deployer Balance:", balance);

        if (balance < 0.002 ether) {
            console.log("ERROR: Insufficient balance for test");
            return;
        }

        vm.startBroadcast(deployerPrivateKey);

        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);

        // Create a simple test message
        bytes memory testMessage = abi.encodeWithSignature("version()");

        console.log("\n=== SENDING SIMPLE TEST MESSAGE ===");
        console.log("Target: L2ScrollMessenger");
        console.log("Message: version() call");
        console.log("Value: 0.001 ETH");
        console.log("Gas Limit: 100000");

        try messenger.sendMessage{value: 0.001 ether}(
            L2_SCROLL_MESSENGER,
            0,
            testMessage,
            100000
        ) {
            console.log("✅ SUCCESS: Simple message sent!");
            console.log("L1ScrollMessenger is working correctly");
        } catch Error(string memory reason) {
            console.log("❌ FAILED: L1ScrollMessenger call failed");
            console.log("Reason:", reason);
        } catch {
            console.log("❌ FAILED: L1ScrollMessenger call failed with unknown error");
            console.log("This suggests an issue with the L1ScrollMessenger");
        }

        vm.stopBroadcast();

        console.log("\n=== ANALYSIS ===");
        console.log("If this simple test succeeds:");
        console.log("- L1ScrollMessenger is working");
        console.log("- The issue is with our L1Attacker contract");
        console.log("- We need to debug the L1Attacker.initiateAttack call");
        
        console.log("\nIf this simple test fails:");
        console.log("- L1ScrollMessenger has issues");
        console.log("- Could be paused, wrong address, or other problems");
        console.log("- Need to investigate L1ScrollMessenger state");
    }
}
