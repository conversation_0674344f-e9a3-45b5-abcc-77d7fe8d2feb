// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";

/**
 * @title TrackMessages
 * @notice Track specific cross-domain messages by nonce
 */
contract TrackMessages is Script {
    
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    address constant DEPLOYER = 0xe00a8a3c66071B44C3aBdFd947Eaa1eA1D70dC6e;
    
    function run() external view {
        console.log("=== TRACKING CROSS-DOMAIN MESSAGES ===");
        console.log("L2ScrollMessenger:", L2_SCROLL_MESSENGER);
        console.log("Deployer:", DEPLOYER);
        console.log("Target Gateway:", L2_CUSTOM_ERC20_GATEWAY);
        
        console.log("\n=== MESSAGE NONCES TO TRACK ===");
        console.log("Attack 1 (Token Mapping): messageNonce 1067113");
        console.log("Attack 2 (Unlimited Minting): messageNonce 1067114");
        
        // Check if messages were executed
        console.log("\n=== CHECKING MESSAGE EXECUTION STATUS ===");
        
        // Create the message hashes to check
        bytes32 hash1 = _getMessageHash(1067113);
        bytes32 hash2 = _getMessageHash(1067114);
        
        console.log("Message Hash 1:", vm.toString(hash1));
        console.log("Message Hash 2:", vm.toString(hash2));
        
        // Try to check execution status
        try this.checkMessageExecuted(hash1) returns (bool executed1) {
            console.log("Message 1067113 executed:", executed1);
        } catch {
            console.log("Could not check message 1067113 status");
        }
        
        try this.checkMessageExecuted(hash2) returns (bool executed2) {
            console.log("Message 1067114 executed:", executed2);
        } catch {
            console.log("Could not check message 1067114 status");
        }
        
        console.log("\n=== MANUAL SEARCH INSTRUCTIONS ===");
        console.log("1. Go to: https://sepolia.scrollscan.com/address/0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d");
        console.log("2. Look for 'relayMessage' transactions");
        console.log("3. Check transaction input data for messageNonces:");
        console.log("   - 1067113 (0x104869 in hex)");
        console.log("   - 1067114 (0x10486A in hex)");
        console.log("4. Or search for transactions TO your address:", DEPLOYER);
        
        console.log("\n=== ALTERNATIVE: CHECK L2 GATEWAY DIRECTLY ===");
        console.log("Go to: https://sepolia.scrollscan.com/address/0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf");
        console.log("Look for 'updateTokenMapping' or 'finalizeDepositERC20' calls");
        console.log("Check if any succeeded with your address as parameter");
        
        console.log("\n=== WHAT SUCCESS LOOKS LIKE ===");
        console.log("If attacks succeeded, you should see:");
        console.log("- relayMessage transactions with your messageNonces");
        console.log("- updateTokenMapping call that succeeded");
        console.log("- finalizeDepositERC20 call that succeeded");
        console.log("- Token balance increase in your address");
        
        console.log("\n=== WHAT FAILURE LOOKS LIKE ===");
        console.log("If access controls worked, you should see:");
        console.log("- relayMessage transactions that REVERTED");
        console.log("- Error: 'ErrorCallerIsNotCounterpartGateway'");
        console.log("- No token balance changes");
        console.log("- No mapping changes");
    }
    
    function _getMessageHash(uint256 messageNonce) internal pure returns (bytes32) {
        // This is a simplified hash - the actual implementation is more complex
        // But it gives us something to search for
        return keccak256(abi.encodePacked("message", messageNonce));
    }
    
    function checkMessageExecuted(bytes32 messageHash) external view returns (bool) {
        // Try to call isL1MessageExecuted on L2ScrollMessenger
        (bool success, bytes memory data) = L2_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("isL1MessageExecuted(bytes32)", messageHash)
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (bool));
        }
        revert("Failed to check message execution");
    }
}
