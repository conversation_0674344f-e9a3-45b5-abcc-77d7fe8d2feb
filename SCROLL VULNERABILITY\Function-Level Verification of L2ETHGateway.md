# Function-Level Verification of L2ETHGateway

## Contract Overview
`L2ETHGateway` is responsible for handling ETH withdrawals from L2 to L1 and finalizing ETH deposits from L1 to L2.

## Key Functions Analysis

### finalizeDepositETH
```solidity
function finalizeDepositETH(
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable override onlyCallByCounterpart nonReentrant {
    require(msg.value == _amount, "msg.value mismatch");

    // solhint-disable-next-line avoid-low-level-calls
    // no reentrancy risk.
    // slither-disable-next-line arbitrary-send-eth
    (bool _success, ) = _to.call{value: _amount}("");
    require(_success, "ETH transfer failed");

    _doCallback(_to, _data);

    emit FinalizeDepositETH(_from, _to, _amount, _data);
}
```

**Access Controls:**
- `onlyCallByCounterpart` modifier - Restricts calls to only come from the messenger contract with xDomainMessageSender set to the counterpart gateway
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that msg.value matches the _amount parameter

**State Changes:**
- Transfers ETH to the recipient (_to)
- Performs callback if _data is provided and _to is a contract

**Vulnerabilities:**
- If the `onlyCallByCounterpart` modifier can be bypassed through the L2ScrollMessenger vulnerability, an attacker could call this function with arbitrary parameters

### _withdraw (Internal)
```solidity
function _withdraw(
    address _to,
    uint256 _amount,
    bytes memory _data,
    uint256 _gasLimit
) internal virtual nonReentrant {
    require(msg.value > 0, "withdraw zero eth");

    // 1. Extract real sender if this call is from L1GatewayRouter.
    address _from = _msgSender();

    if (router == _from) {
        (_from, _data) = abi.decode(_data, (address, bytes));
    }

    // @note no rate limit here, since ETH is limited in messenger

    bytes memory _message = abi.encodeCall(IL1ETHGateway.finalizeWithdrawETH, (_from, _to, _amount, _data));
    IL2ScrollMessenger(messenger).sendMessage{value: msg.value}(counterpart, _amount, _message, _gasLimit);

    emit WithdrawETH(_from, _to, _amount, _data);
}
```

**Access Controls:**
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that msg.value is greater than 0

**State Changes:**
- Sends a message to L1 via the messenger contract

# Function-Level Verification of L1ETHGateway

## Contract Overview
`L1ETHGateway` handles ETH deposits from L1 to L2 and finalizes ETH withdrawals from L2 to L1.

## Key Functions Analysis

### finalizeWithdrawETH
```solidity
function finalizeWithdrawETH(
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable override onlyCallByCounterpart nonReentrant {
    require(msg.value == _amount, "msg.value mismatch");

    // @note can possible trigger reentrant call to messenger,
    // but it seems not a big problem.
    // no reentrancy risk (nonReentrant modifier).
    // slither-disable-next-line arbitrary-send-eth
    (bool _success, ) = _to.call{value: _amount}("");
    require(_success, "ETH transfer failed");

    _doCallback(_to, _data);

    emit FinalizeWithdrawETH(_from, _to, _amount, _data);
}
```

**Access Controls:**
- `onlyCallByCounterpart` modifier - Restricts calls to only come from the messenger contract with xDomainMessageSender set to the counterpart gateway
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that msg.value matches the _amount parameter

**State Changes:**
- Transfers ETH to the recipient (_to)
- Performs callback if _data is provided and _to is a contract

# Function-Level Verification of L2GatewayRouter

## Contract Overview
`L2GatewayRouter` is the main entry point for withdrawing ETH and ERC20 tokens from L2 to L1.

## Key Functions Analysis

### setETHGateway
```solidity
function setETHGateway(address _newEthGateway) external onlyOwner {
    address _oldEthGateway = ethGateway;
    ethGateway = _newEthGateway;

    emit SetETHGateway(_oldEthGateway, _newEthGateway);
}
```

**Access Controls:**
- `onlyOwner` modifier - Restricts calls to only the contract owner

**Input Validation:**
- None for the _newEthGateway parameter

**State Changes:**
- Updates the ethGateway address

**Vulnerabilities:**
- If the `onlyOwner` modifier can be bypassed through the L2ScrollMessenger vulnerability, an attacker could redirect all ETH withdrawals to a malicious gateway

### setDefaultERC20Gateway
```solidity
function setDefaultERC20Gateway(address _newDefaultERC20Gateway) external onlyOwner {
    address _oldDefaultERC20Gateway = defaultERC20Gateway;
    defaultERC20Gateway = _newDefaultERC20Gateway;

    emit SetDefaultERC20Gateway(_oldDefaultERC20Gateway, _newDefaultERC20Gateway);
}
```

**Access Controls:**
- `onlyOwner` modifier - Restricts calls to only the contract owner

**Input Validation:**
- None for the _newDefaultERC20Gateway parameter

**State Changes:**
- Updates the defaultERC20Gateway address

**Vulnerabilities:**
- If the `onlyOwner` modifier can be bypassed, an attacker could redirect all default ERC20 withdrawals to a malicious gateway

### setERC20Gateway
```solidity
function setERC20Gateway(address[] memory _tokens, address[] memory _gateways) external onlyOwner {
    require(_tokens.length == _gateways.length, "length mismatch");

    for (uint256 i = 0; i < _tokens.length; i++) {
        address _oldGateway = ERC20Gateway[_tokens[i]];
        ERC20Gateway[_tokens[i]] = _gateways[i];

        emit SetERC20Gateway(_tokens[i], _oldGateway, _gateways[i]);
    }
}
```

**Access Controls:**
- `onlyOwner` modifier - Restricts calls to only the contract owner

**Input Validation:**
- Validates that _tokens and _gateways arrays have the same length

**State Changes:**
- Updates the ERC20Gateway mapping for multiple tokens

**Vulnerabilities:**
- If the `onlyOwner` modifier can be bypassed, an attacker could redirect specific token withdrawals to malicious gateways

# Function-Level Verification of L2ScrollMessenger

## Contract Overview
`L2ScrollMessenger` handles cross-domain messaging between L1 and L2.

## Key Functions Analysis

### relayMessage
```solidity
function relayMessage(
    address _from,
    address _to,
    uint256 _value,
    uint256 _nonce,
    bytes memory _message
) external override whenNotPaused {
    // It is impossible to deploy a contract with the same address, reentrance is prevented in nature.
    require(AddressAliasHelper.undoL1ToL2Alias(_msgSender()) == counterpart, "Caller is not L1ScrollMessenger");

    bytes32 _xDomainCalldataHash = keccak256(_encodeXDomainCalldata(_from, _to, _value, _nonce, _message));

    require(!isL1MessageExecuted[_xDomainCalldataHash], "Message was already successfully executed");

    _executeMessage(_from, _to, _value, _message, _xDomainCalldataHash);
}
```

**Access Controls:**
- `whenNotPaused` modifier - Prevents execution when the contract is paused
- Requires caller to be the L1ScrollMessenger (after address aliasing)

**Input Validation:**
- Checks that the message hasn't been executed before

**State Changes:**
- Calls _executeMessage which may update isL1MessageExecuted mapping

### _executeMessage (Internal)
```solidity
function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    // @note check more `_to` address to avoid attack in the future when we add more gateways.
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);

    // @note This usually will never happen, just in case.
    require(_from != xDomainMessageSender, "Invalid message sender");

    xDomainMessageSender = _from;
    // solhint-disable-next-line avoid-low-level-calls
    // no reentrancy risk, only alias(l1ScrollMessenger) can call relayMessage.
    // slither-disable-next-line reentrancy-eth
    (bool success, ) = _to.call{value: _value}(_message);
    // reset value to refund gas.
    xDomainMessageSender = ScrollConstants.DEFAULT_XDOMAIN_MESSAGE_SENDER;

    if (success) {
        isL1MessageExecuted[_xDomainCalldataHash] = true;
        emit RelayedMessage(_xDomainCalldataHash);
    } else {
        emit FailedRelayedMessage(_xDomainCalldataHash);
    }
}
```

**Access Controls:**
- None directly, but called from relayMessage which has access controls

**Input Validation:**
- Validates that _to is not the messageQueue
- Calls _validateTargetAddress(_to) which only checks that _to is not the messenger itself
- Validates that _from is not the current xDomainMessageSender

**State Changes:**
- Sets xDomainMessageSender to _from before the call and resets it after
- Updates isL1MessageExecuted mapping if the call succeeds

**Vulnerabilities:**
- The critical vulnerability is in the insufficient validation in _validateTargetAddress
- Only prevents calls to the messenger itself and messageQueue, but not other critical contracts
- An attacker could potentially call any other contract with arbitrary data

# Function-Level Verification of ScrollGatewayBase

## Contract Overview
`ScrollGatewayBase` is the base contract for all gateway contracts in both L1 and L2.

## Key Functions Analysis

### onlyCallByCounterpart Modifier
```solidity
modifier onlyCallByCounterpart() {
    // check caller is messenger
    if (_msgSender() != messenger) {
        revert ErrorCallerIsNotMessenger();
    }

    // check cross domain caller is counterpart gateway
    if (counterpart != IScrollMessenger(messenger).xDomainMessageSender()) {
        revert ErrorCallerIsNotCounterpartGateway();
    }
    _;
}
```

**Access Controls:**
- Checks that the caller is the messenger contract
- Checks that the xDomainMessageSender is the counterpart gateway

**Vulnerabilities:**
- If an attacker can control the xDomainMessageSender value in the messenger contract, they could bypass this check

### _doCallback (Internal)
```solidity
function _doCallback(address _to, bytes memory _data) internal {
    if (_data.length > 0 && _to.code.length > 0) {
        IScrollGatewayCallback(_to).onScrollGatewayCallback(_data);
    }
}
```

**Access Controls:**
- None directly, but typically called from functions with access controls

**Input Validation:**
- Checks that _data is not empty and _to is a contract

**State Changes:**
- Calls the onScrollGatewayCallback function on the _to contract

**Vulnerabilities:**
- If called with attacker-controlled parameters, could lead to arbitrary contract calls
