// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {IL1ScrollMessenger} from "../src/L1/IL1ScrollMessenger.sol";

/**
 * @title SimpleTest
 * @notice Send a simple, benign cross-domain message to test sequencer processing
 */
contract SimpleTest is Script {
    
    // Scroll Sepolia addresses
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        console.log("=== SIMPLE MESSAGE TEST ===");
        console.log("Testing if sequencer processes normal messages");
        console.log("Deployer:", deployer);
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("L2ScrollMessenger:", L2_SCROLL_MESSENGER);
        
        // Check balance
        uint256 balance = deployer.balance;
        console.log("Deployer Balance:", balance);
        
        if (balance < 0.002 ether) {
            console.log("ERROR: Insufficient balance for test message");
            return;
        }
        
        vm.startBroadcast(deployerPrivateKey);
        
        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);
        
        // Create a simple, harmless message
        // We'll just send a message to the L2ScrollMessenger itself
        // This should always work and not trigger any access controls
        bytes memory simpleMessage = abi.encodeWithSignature(
            "version()"  // Just call a view function - completely harmless
        );
        
        console.log("\n=== SENDING SIMPLE MESSAGE ===");
        console.log("Target: L2ScrollMessenger (itself)");
        console.log("Message: version() call");
        console.log("This should be processed quickly if sequencer is working");
        
        try messenger.sendMessage{value: 0.002 ether}(
            L2_SCROLL_MESSENGER,    // target: L2ScrollMessenger itself
            0,                      // value
            simpleMessage,          // harmless message
            100000                  // gasLimit
        ) {
            console.log("SUCCESS: Simple message sent!");
            console.log("This message should be processed quickly");
            console.log("Compare processing time with our attack messages");
        } catch Error(string memory reason) {
            console.log("FAILED: Simple message failed");
            console.log("Reason:", reason);
            console.log("This suggests a broader issue with L1ScrollMessenger");
        } catch {
            console.log("FAILED: Simple message failed with unknown error");
        }
        
        vm.stopBroadcast();
        
        console.log("\n=== COMPARISON TEST ===");
        console.log("Our attack messages (sent ~2 hours ago):");
        console.log("- Attack 1: messageNonce 1067113");
        console.log("- Attack 2: messageNonce 1067114");
        console.log("- Status: Still not processed");
        
        console.log("\nThis simple message (just sent):");
        console.log("- Will get a new messageNonce (likely 1067115+)");
        console.log("- Should be processed quickly if sequencer is working");
        console.log("- If it processes faster, our attack messages might be stuck");
        
        console.log("\n=== MONITORING INSTRUCTIONS ===");
        console.log("1. Note the messageNonce from this transaction");
        console.log("2. Monitor L2ScrollMessenger for this new message");
        console.log("3. Compare processing time with our attack messages");
        console.log("4. If this processes quickly but attacks don't:");
        console.log("   - Our attack messages might be rejected/stuck");
        console.log("   - Could indicate access controls are working");
        console.log("   - Or there's something wrong with our message format");
        
        console.log("\n=== NEXT STEPS ===");
        console.log("Wait 10-15 minutes and check:");
        console.log("- L2ScrollMessenger for relayMessage with new messageNonce");
        console.log("- Compare with status of messageNonces 1067113 and 1067114");
        console.log("- This will tell us if the issue is general delay or specific to our attacks");
    }
}
