# Function-Level Verification of L1ERC20Gateway

## Contract Overview
`L1ERC20Gateway` is an abstract base contract for ERC20 gateways in L1, providing common functionality for depositing ERC20 tokens from L1 to L2 and finalizing withdrawals from L2 to L1.

## Key Functions Analysis

### finalizeWithdrawERC20
```solidity
function finalizeWithdrawERC20(
    address _l1Token,
    address _l2Token,
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable virtual override onlyCallByCounterpart nonReentrant {
    _beforeFinalizeWithdrawERC20(_l1Token, _l2Token, _from, _to, _amount, _data);

    // @note can possible trigger reentrant call to this contract or messenger,
    // but it seems not a big problem.
    IERC20Upgradeable(_l1Token).safeTransfer(_to, _amount);

    _doCallback(_to, _data);

    emit FinalizeWithdrawERC20(_l1Token, _l2Token, _from, _to, _amount, _data);
}
```

**Access Controls:**
- `onlyCallByCounterpart` modifier - Restricts calls to only come from the messenger contract with xDomainMessageSender set to the counterpart gateway
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Calls `_beforeFinalizeWithdrawERC20` which is implemented by derived contracts to perform additional validation

**State Changes:**
- Transfers ERC20 tokens to the recipient (_to)
- Performs callback if _data is provided and _to is a contract

**Vulnerabilities:**
- If the `onlyCallByCounterpart` modifier can be bypassed through the L2ScrollMessenger vulnerability, an attacker could call this function with arbitrary parameters

### onDropMessage
```solidity
function onDropMessage(bytes calldata _message) external payable virtual onlyInDropContext nonReentrant {
    // _message should start with 0x8431f5c1  =>  finalizeDepositERC20(address,address,address,address,uint256,bytes)
    require(bytes4(_message[0:4]) == IL2ERC20Gateway.finalizeDepositERC20.selector, "invalid selector");

    // decode (token, receiver, amount)
    (address _token, , address _receiver, , uint256 _amount, ) = abi.decode(
        _message[4:],
        (address, address, address, address, uint256, bytes)
    );

    // do dome check for each custom gateway
    _beforeDropMessage(_token, _receiver, _amount);

    IERC20Upgradeable(_token).safeTransfer(_receiver, _amount);

    emit RefundERC20(_token, _receiver, _amount);
}
```

**Access Controls:**
- `onlyInDropContext` modifier - Ensures the function is called in the context of dropping a message
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that the message selector matches finalizeDepositERC20
- Calls `_beforeDropMessage` which is implemented by derived contracts to perform additional validation

**State Changes:**
- Transfers ERC20 tokens back to the receiver in case of a dropped message

# Function-Level Verification of L1StandardERC20Gateway

## Contract Overview
`L1StandardERC20Gateway` extends L1ERC20Gateway to handle standard ERC20 tokens, depositing them from L1 to L2 and finalizing withdrawals from L2 to L1.

## Key Functions Analysis

### _deposit (Internal)
```solidity
function _deposit(
    address _token,
    address _to,
    uint256 _amount,
    bytes memory _data,
    uint256 _gasLimit
) internal virtual override nonReentrant {
    require(_amount > 0, "deposit zero amount");

    // 1. Transfer token into this contract.
    address _from;
    (_from, _amount, _data) = _transferERC20In(_token, _amount, _data);

    // 2. Generate message passed to L2StandardERC20Gateway.
    address _l2Token = tokenMapping[_token];
    bytes memory _l2Data;
    if (_l2Token == address(0)) {
        // @note we won't update `tokenMapping` here but update the `tokenMapping` on
        // first successful withdraw. This will prevent user to set arbitrary token
        // metadata by setting a very small `_gasLimit` on the first tx.
        _l2Token = getL2ERC20Address(_token);

        // passing symbol/name/decimal in order to deploy in L2.
        string memory _symbol = IERC20MetadataUpgradeable(_token).symbol();
        string memory _name = IERC20MetadataUpgradeable(_token).name();
        uint8 _decimals = IERC20MetadataUpgradeable(_token).decimals();
        _l2Data = abi.encode(true, abi.encode(_data, abi.encode(_symbol, _name, _decimals)));
    } else {
        _l2Data = abi.encode(false, _data);
    }
    bytes memory _message = abi.encodeCall(
        IL2ERC20Gateway.finalizeDepositERC20,
        (_token, _l2Token, _from, _to, _amount, _l2Data)
    );

    // 3. Send message to L1ScrollMessenger.
    IL1ScrollMessenger(messenger).sendMessage{value: msg.value}(counterpart, 0, _message, _gasLimit, _from);

    emit DepositERC20(_token, _l2Token, _from, _to, _amount, _data);
}
```

**Access Controls:**
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that _amount is greater than 0

**State Changes:**
- Transfers ERC20 tokens from the sender to this contract
- Sends a message to L2 via the messenger contract

**Vulnerabilities:**
- No direct vulnerabilities in this function, but it relies on the security of the messenger contract

### _beforeFinalizeWithdrawERC20 (Internal)
```solidity
function _beforeFinalizeWithdrawERC20(
    address _l1Token,
    address _l2Token,
    address,
    address,
    uint256,
    bytes calldata
) internal virtual override {
    require(msg.value == 0, "nonzero msg.value");
    require(_l2Token != address(0), "token address cannot be 0");
    require(getL2ERC20Address(_l1Token) == _l2Token, "l2 token mismatch");

    // update `tokenMapping` on first withdraw
    address _storedL2Token = tokenMapping[_l1Token];
    if (_storedL2Token == address(0)) {
        tokenMapping[_l1Token] = _l2Token;
    } else {
        require(_storedL2Token == _l2Token, "l2 token mismatch");
    }
}
```

**Access Controls:**
- None directly, but called from finalizeWithdrawERC20 which has access controls

**Input Validation:**
- Validates that msg.value is 0
- Validates that _l2Token is not the zero address
- Validates that _l2Token matches the computed L2 token address for _l1Token

**State Changes:**
- Updates tokenMapping on first withdrawal

# Function-Level Verification of L2StandardERC20Gateway

## Contract Overview
`L2StandardERC20Gateway` handles standard ERC20 token withdrawals from L2 to L1 and finalizes deposits from L1 to L2.

## Key Functions Analysis

### finalizeDepositERC20
```solidity
function finalizeDepositERC20(
    address _l1Token,
    address _l2Token,
    address _from,
    address _to,
    uint256 _amount,
    bytes memory _data
) external payable override onlyCallByCounterpart nonReentrant {
    require(msg.value == 0, "nonzero msg.value");
    require(_l1Token != address(0), "token address cannot be 0");

    {
        // avoid stack too deep
        address _expectedL2Token = IScrollStandardERC20Factory(tokenFactory).computeL2TokenAddress(
            address(this),
            _l1Token
        );
        require(_l2Token == _expectedL2Token, "l2 token mismatch");
    }

    bool _hasMetadata;
    (_hasMetadata, _data) = abi.decode(_data, (bool, bytes));

    bytes memory _deployData;
    bytes memory _callData;

    if (_hasMetadata) {
        (_callData, _deployData) = abi.decode(_data, (bytes, bytes));
    } else {
        require(tokenMapping[_l2Token] == _l1Token, "token mapping mismatch");
        _callData = _data;
    }

    if (!_l2Token.isContract()) {
        // first deposit, update mapping
        tokenMapping[_l2Token] = _l1Token;

        _deployL2Token(_deployData, _l1Token);
    }

    IScrollERC20Upgradeable(_l2Token).mint(_to, _amount);

    _doCallback(_to, _callData);

    emit FinalizeDepositERC20(_l1Token, _l2Token, _from, _to, _amount, _callData);
}
```

**Access Controls:**
- `onlyCallByCounterpart` modifier - Restricts calls to only come from the messenger contract with xDomainMessageSender set to the counterpart gateway
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that msg.value is 0
- Validates that _l1Token is not the zero address
- Validates that _l2Token matches the computed L2 token address for _l1Token
- If not first deposit, validates that the token mapping is correct

**State Changes:**
- If first deposit, deploys the L2 token and updates tokenMapping
- Mints tokens to the recipient (_to)
- Performs callback if _callData is provided and _to is a contract

**Vulnerabilities:**
- If the `onlyCallByCounterpart` modifier can be bypassed through the L2ScrollMessenger vulnerability, an attacker could call this function with arbitrary parameters, potentially minting unlimited tokens

### _withdraw (Internal)
```solidity
function _withdraw(
    address _token,
    address _to,
    uint256 _amount,
    bytes memory _data,
    uint256 _gasLimit
) internal virtual override nonReentrant {
    require(_amount > 0, "withdraw zero amount");

    // 1. Extract real sender if this call is from L2GatewayRouter.
    address _from = _msgSender();
    if (router == _from) {
        (_from, _data) = abi.decode(_data, (address, bytes));
    }

    address _l1Token = tokenMapping[_token];
    require(_l1Token != address(0), "no corresponding l1 token");

    // 2. Burn token.
    IScrollERC20Upgradeable(_token).burn(_from, _amount);

    // 3. Generate message passed to L1StandardERC20Gateway.
    bytes memory _message = abi.encodeCall(
        IL1ERC20Gateway.finalizeWithdrawERC20,
        (_l1Token, _token, _from, _to, _amount, _data)
    );

    // 4. send message to L2ScrollMessenger
    IL2ScrollMessenger(messenger).sendMessage{value: msg.value}(counterpart, 0, _message, _gasLimit);

    emit WithdrawERC20(_l1Token, _token, _from, _to, _amount, _data);
}
```

**Access Controls:**
- `nonReentrant` modifier - Prevents reentrancy attacks

**Input Validation:**
- Validates that _amount is greater than 0
- Validates that there is a corresponding L1 token for the L2 token

**State Changes:**
- Burns tokens from the sender (_from)
- Sends a message to L1 via the messenger contract

# Cross-Contract Vulnerability Analysis

Based on the function-level verification of these contracts, I can identify the following key vulnerabilities that could be exploited through the insufficient validation in L2ScrollMessenger:

1. **Unlimited Token Minting (Critical)**
   - If an attacker can bypass the `onlyCallByCounterpart` modifier in L2StandardERC20Gateway.finalizeDepositERC20, they could mint unlimited amounts of any L2 token
   - This is possible because the L2ScrollMessenger._executeMessage function sets xDomainMessageSender to the attacker-controlled _from parameter
   - The attacker could craft a message that calls finalizeDepositERC20 with arbitrary parameters, including token addresses and amounts

2. **Gateway Routing Manipulation (Critical)**
   - If an attacker can bypass the `onlyOwner` modifier in L2GatewayRouter's administrative functions (setETHGateway, setDefaultERC20Gateway, setERC20Gateway), they could redirect all withdrawals to malicious contracts
   - This would allow the attacker to steal all future withdrawals of ETH and tokens

3. **Unauthorized Token Transfers (Critical)**
   - If an attacker can bypass the `onlyCallByCounterpart` modifier in L1ETHGateway.finalizeWithdrawETH or L1ERC20Gateway.finalizeWithdrawERC20, they could trigger unauthorized transfers of ETH or tokens from these contracts
   - This could potentially drain all ETH and tokens held in these gateway contracts

These vulnerabilities are particularly severe because they affect the core functionality of the bridge and could lead to significant financial losses.
