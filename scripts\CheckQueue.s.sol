// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";

/**
 * @title CheckQueue
 * @notice Check the message queue status and processing
 */
contract CheckQueue is Script {
    
    // L1 Message Queue
    address constant L1_MESSAGE_QUEUE = ******************************************;
    
    // L2 ScrollMessenger
    address constant L2_SCROLL_MESSENGER = ******************************************;
    
    function run() external view {
        console.log("=== CHECKING MESSAGE QUEUE STATUS ===");
        console.log("L1 Message Queue:", L1_MESSAGE_QUEUE);
        console.log("L2 ScrollMessenger:", L2_SCROLL_MESSENGER);
        
        console.log("\n=== YOUR MESSAGE NONCES ===");
        console.log("Attack 1: 1067113 (0x104869)");
        console.log("Attack 2: 1067114 (0x10486A)");
        
        // Check current queue status
        console.log("\n=== QUEUE STATUS CHECK ===");
        
        try this.getNextMessageIndex() returns (uint256 nextIndex) {
            console.log("Next message index in queue:", nextIndex);
            if (nextIndex > 1067114) {
                console.log("✅ Your messages should have been processed");
                console.log("Check L2 for execution results");
            } else {
                console.log("⏳ Your messages are still waiting in queue");
                console.log("Messages ahead of yours:", 1067113 - nextIndex);
            }
        } catch {
            console.log("Could not check queue status");
        }
        
        // Check if sequencer is active
        console.log("\n=== SEQUENCER ACTIVITY CHECK ===");
        try this.getLastProcessedMessage() returns (uint256 lastProcessed) {
            console.log("Last processed message nonce:", lastProcessed);
            if (lastProcessed >= 1067114) {
                console.log("✅ Sequencer has processed your messages");
            } else if (lastProcessed >= 1067113) {
                console.log("⚠️ First message processed, second still pending");
            } else {
                console.log("⏳ Sequencer hasn't reached your messages yet");
            }
        } catch {
            console.log("Could not check last processed message");
        }
        
        console.log("\n=== MANUAL VERIFICATION STEPS ===");
        console.log("1. Check L1 Message Queue recent activity:");
        console.log("   https://sepolia.etherscan.io/address/******************************************");
        
        console.log("\n2. Check L2 ScrollMessenger for recent relayMessage calls:");
        console.log("   https://sepolia.scrollscan.com/address/******************************************");
        
        console.log("\n3. Search for your specific messageNonces in transaction data:");
        console.log("   - Look for '104869' (hex for 1067113)");
        console.log("   - Look for '10486A' (hex for 1067114)");
        
        console.log("\n=== WHAT TO DO NEXT ===");
        console.log("If messages are still pending:");
        console.log("- Wait longer (sometimes takes hours)");
        console.log("- Check Scroll status page for issues");
        console.log("- Monitor L2ScrollMessenger for activity");
        
        console.log("\nIf messages were processed:");
        console.log("- Check transaction success/failure");
        console.log("- Look for revert reasons");
        console.log("- Check token balances and mappings");
    }
    
    function getNextMessageIndex() external view returns (uint256) {
        (bool success, bytes memory data) = L1_MESSAGE_QUEUE.staticcall(
            abi.encodeWithSignature("nextCrossDomainMessageIndex()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (uint256));
        }
        revert("Failed to get next message index");
    }
    
    function getLastProcessedMessage() external view returns (uint256) {
        // This is a simplified check - actual implementation may vary
        (bool success, bytes memory data) = L2_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("lastProcessedMessageIndex()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (uint256));
        }
        revert("Failed to get last processed message");
    }
}
