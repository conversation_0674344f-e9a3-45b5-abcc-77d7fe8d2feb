name: Tests

on: [push, pull_request]

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Install Foundry
        uses: onbjerg/foundry-toolchain@v1
        with:
          version: nightly

      - name: Install dependencies
        run: forge install

      - name: Check contract sizes
        run: forge build --sizes

      - name: Check gas snapshots
        run: forge snapshot --check

      - name: Run tests
        run: forge test
        env:
          # Only fuzz intensely if we're running this action on a push to main or for a PR going into main:
          FOUNDRY_PROFILE: ${{ (github.ref == 'refs/heads/main' || github.base_ref == 'main') && 'intense' }}
