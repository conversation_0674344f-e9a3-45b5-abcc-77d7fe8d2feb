// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";

/**
 * @title CheckMessenger
 * @notice Check the state of L1ScrollMessenger
 */
contract CheckMessenger is Script {
    
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7C656493d4cc2E4D9D203B23BBc8c3a;
    
    function run() external view {
        console.log("=== CHECKING L1SCROLLMESSENGER STATE ===");
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        
        // Check if contract exists
        uint256 codeSize;
        assembly {
            codeSize := extcodesize(L1_SCROLL_MESSENGER)
        }
        console.log("Contract code size:", codeSize);
        
        if (codeSize == 0) {
            console.log("ERROR: L1ScrollMessenger has no code!");
            console.log("The address might be incorrect for <PERSON><PERSON> Sepolia");
            return;
        }
        
        // Try to call some view functions to check state
        try this.checkPaused() returns (bool paused) {
            console.log("Is paused:", paused);
            if (paused) {
                console.log("ERROR: L1ScrollMessenger is paused!");
            }
        } catch {
            console.log("Could not check paused state");
        }
        
        try this.checkCounterpart() returns (address counterpart) {
            console.log("Counterpart (L2ScrollMessenger):", counterpart);
        } catch {
            console.log("Could not check counterpart");
        }
        
        try this.checkMessageQueue() returns (address messageQueue) {
            console.log("Message Queue:", messageQueue);
        } catch {
            console.log("Could not check message queue");
        }
        
        console.log("\n=== RECOMMENDATIONS ===");
        console.log("1. Verify the L1ScrollMessenger address is correct for Scroll Sepolia");
        console.log("2. Check if the bridge is operational");
        console.log("3. Try with a different gas limit or fee");
        console.log("4. Check Scroll documentation for current testnet status");
    }
    
    function checkPaused() external view returns (bool) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("paused()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (bool));
        }
        revert("Failed to check paused state");
    }
    
    function checkCounterpart() external view returns (address) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("counterpart()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (address));
        }
        revert("Failed to check counterpart");
    }
    
    function checkMessageQueue() external view returns (address) {
        (bool success, bytes memory data) = L1_SCROLL_MESSENGER.staticcall(
            abi.encodeWithSignature("messageQueue()")
        );
        if (success && data.length >= 32) {
            return abi.decode(data, (address));
        }
        revert("Failed to check message queue");
    }
}
