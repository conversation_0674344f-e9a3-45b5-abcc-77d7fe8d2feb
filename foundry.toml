[profile.default]
src = 'src'                                             # the source directory
test = 'src/test'                                       # the test directory
# script = 'scripts'                                      # the script directory
out = 'artifacts/src'                                   # the output directory (for artifacts)
libs = ["lib"]
remappings = []                                               # a list of remappings
libraries = []                                                # a list of deployed libraries to link against
cache = true                                                  # whether to cache builds or not
# force = true                                                # whether to ignore the cache (clean build)
evm_version = 'cancun'                                        # the evm version (by hardfork name)
solc_version = '0.8.24'                                       # override for the solc version (setting this ignores `auto_detect_solc`)
optimizer = true                                              # enable or disable the solc optimizer
optimizer_runs = 200                                          # the number of optimizer runs
verbosity = 2                                                 # the verbosity of tests
ignored_error_codes = []                                      # a list of ignored solc error codes
fuzz_runs = 256                                               # the number of fuzz runs for tests
ffi = false                                                   # whether to enable ffi or not
sender = '******************************************'         # the address of `msg.sender` in tests
tx_origin = '******************************************'      # the address of `tx.origin` in tests
initial_balance = '0xffffffffffffffffffffffff'                # the initial balance of the test contract
block_number = 0                                              # the block number we are at in tests
gas_limit = 9223372036854775807                               # the gas limit in tests
gas_price = 0                                                 # the gas price (in wei) in tests
block_base_fee_per_gas = 0                                    # the base fee (in wei) in tests
block_coinbase = '******************************************' # the address of `block.coinbase` in tests
block_timestamp = 0                                           # the value of `block.timestamp` in tests
block_difficulty = 0                                          # the value of `block.difficulty` in tests

gas_reports = ["L2GasPriceOracle"]
