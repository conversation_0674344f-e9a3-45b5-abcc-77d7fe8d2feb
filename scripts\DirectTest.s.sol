// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {IL1ScrollMessenger} from "../src/L1/IL1ScrollMessenger.sol";

/**
 * @title DirectTest
 * @notice Direct test of L1ScrollMessenger to understand the issue
 */
contract DirectTest is Script {

    // Scroll Sepolia addresses (CORRECTED)
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    address constant L2_TEST_TOKEN = 0x7DB1015435D34Ae12FF5a42FE4b6c429734617FD;

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== DIRECT L1SCROLLMESSENGER TEST ===");
        console.log("Deployer:", deployer);
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("Target L2 Gateway:", L2_CUSTOM_ERC20_GATEWAY);

        // Check deployer balance
        uint256 balance = deployer.balance;
        console.log("Deployer Balance:", balance);

        if (balance < 0.01 ether) {
            console.log("ERROR: Insufficient balance");
            return;
        }

        vm.startBroadcast(deployerPrivateKey);

        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);

        // Create a simple test message
        bytes memory testMessage = abi.encodeWithSignature(
            "updateTokenMapping(address,address)",
            L2_TEST_TOKEN,
            deployer  // Use deployer address as the "malicious" L1 token
        );

        console.log("=== SENDING TEST MESSAGE ===");
        console.log("Message data length:", testMessage.length);
        console.log("Gas limit: 200000");
        console.log("Value: 0.01 ETH");

        try messenger.sendMessage{value: 0.01 ether}(
            L2_CUSTOM_ERC20_GATEWAY,  // target
            0,                        // value
            testMessage,              // data
            200000                    // gasLimit
        ) {
            console.log("SUCCESS: Message sent to L1ScrollMessenger!");
            console.log("This proves the basic functionality works");
            console.log("The _from parameter in L2 should be:", deployer);
            console.log("Wait 5-10 minutes and check Scroll Sepolia Explorer");
        } catch Error(string memory reason) {
            console.log("FAILED: L1ScrollMessenger call failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: L1ScrollMessenger call failed with unknown error");
            console.log("This suggests a fundamental issue with the setup");
        }

        vm.stopBroadcast();

        console.log("\n=== ANALYSIS ===");
        console.log("If this test succeeds:");
        console.log("- The L1ScrollMessenger accepts our message");
        console.log("- The _from parameter will be the deployer address");
        console.log("- This proves _from IS user-controlled!");
        console.log("");
        console.log("If this test fails:");
        console.log("- There might be an issue with the L1ScrollMessenger");
        console.log("- Or the gas fees are still insufficient");
        console.log("- Or there are other validation requirements");
    }
}
