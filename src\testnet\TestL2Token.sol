// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {IScrollERC20Upgradeable} from "../libraries/token/IScrollERC20Upgradeable.sol";
import {ERC20Upgradeable} from "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import {OwnableUpgradeable} from "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";

/**
 * @title TestL2Token
 * @notice A simple L2 ERC20 token for testnet POC
 *
 * This token implements the IScrollERC20Upgradeable interface so it can be used
 * with the L2CustomERC20Gateway for our vulnerability demonstration.
 */
contract TestL2Token is ERC20Upgradeable, OwnableUpgradeable, IScrollERC20Upgradeable {

    /// @notice The gateway contract that can mint/burn tokens
    address public gateway;

    /// @notice The corresponding L1 token address
    address public counterpart;

    /// @notice Token decimals
    uint8 private _decimals;

    event GatewayUpdated(address indexed oldGateway, address indexed newGateway);
    event CounterpartUpdated(address indexed oldCounterpart, address indexed newCounterpart);

    /**
     * @notice Initialize the token
     * @param _name Token name
     * @param _symbol Token symbol
     * @param _decimals Token decimals
     * @param _gateway The gateway contract address
     * @param _counterpart The L1 token address
     */
    function initialize(
        string memory _name,
        string memory _symbol,
        uint8 _decimals,
        address _gateway,
        address _counterpart
    ) external initializer {
        __ERC20_init(_name, _symbol);
        __Ownable_init();

        gateway = _gateway;
        counterpart = _counterpart;
        _decimals = _decimals;
    }

    /**
     * @notice Mint tokens (only callable by gateway)
     * @param _to Recipient address
     * @param _amount Amount to mint
     */
    function mint(address _to, uint256 _amount) external override {
        require(msg.sender == gateway, "TestL2Token: only gateway can mint");
        _mint(_to, _amount);
    }

    /**
     * @notice Burn tokens (only callable by gateway)
     * @param _from Address to burn from
     * @param _amount Amount to burn
     */
    function burn(address _from, uint256 _amount) external override {
        require(msg.sender == gateway, "TestL2Token: only gateway can burn");
        _burn(_from, _amount);
    }

    /**
     * @notice Get the gateway address
     * @return The gateway contract address
     */
    function gateway() external view override returns (address) {
        return gateway;
    }

    /**
     * @notice Get the counterpart L1 token address
     * @return The L1 token address
     */
    function counterpart() external view override returns (address) {
        return counterpart;
    }

    /**
     * @notice Update the gateway address (only owner)
     * @param _newGateway New gateway address
     */
    function updateGateway(address _newGateway) external onlyOwner {
        address oldGateway = gateway;
        gateway = _newGateway;
        emit GatewayUpdated(oldGateway, _newGateway);
    }

    /**
     * @notice Update the counterpart L1 token address (only owner)
     * @param _newCounterpart New L1 token address
     */
    function updateCounterpart(address _newCounterpart) external onlyOwner {
        address oldCounterpart = counterpart;
        counterpart = _newCounterpart;
        emit CounterpartUpdated(oldCounterpart, _newCounterpart);
    }

    /**
     * @notice Override decimals function
     * @return Number of decimals
     */
    function decimals() public view override returns (uint8) {
        return _decimals == 0 ? 18 : _decimals; // Default to 18 if not set
    }
}
