// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {L1AttackerContract} from "../src/testnet/L1AttackerContract.sol";

/**
 * @title DeployL1A<PERSON>cker
 * @notice Deploy the L1 attacker contract on Sepolia
 */
contract DeployL1<PERSON><PERSON>cker is <PERSON>ript {

    // Scroll Sepolia L1ScrollMessenger address
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7C656493d4cc2E4D9D203B23BBc8c3a;

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        console.log("Deploying L1AttackerContract on Sepolia...");
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("Deployer:", vm.addr(deployerPrivateKey));

        L1AttackerContract attacker = new L1AttackerContract(L1_SCROLL_MESSENGER);

        console.log("L1AttackerContract deployed at:", address(attacker));
        console.log("Attacker address (will be _from in L2):", attacker.getAttackerAddress());

        vm.stopBroadcast();

        // Save deployment info
        console.log("\n=== DEPLOYMENT COMPLETE ===");
        console.log("Network: Sepolia");
        console.log("L1AttackerContract:", address(attacker));
        console.log("L1ScrollMessenger:", L1_SCROLL_MESSENGER);
        console.log("\nNext steps:");
        console.log("1. Fund the attacker contract with ETH for gas fees");
        console.log("2. Deploy L2 test token on Scroll Sepolia");
        console.log("3. Execute attacks and monitor L2 transactions");
    }
}
