// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {TestL2Token} from "../src/testnet/TestL2Token.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

/**
 * @title DeployL2Token
 * @notice Deploy the test L2 token on Scroll Sepolia
 */
contract DeployL2Token is Script {
    
    // Scroll Sepolia L2CustomERC20Gateway address
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x9aD3c5617eCAa556d6E166787A97081907171230;
    
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        vm.startBroadcast(deployerPrivateKey);
        
        console.log("Deploying TestL2Token on Scroll Sepolia...");
        console.log("L2CustomERC20Gateway:", L2_CUSTOM_ERC20_GATEWAY);
        console.log("Deployer:", deployer);
        
        // Deploy implementation
        TestL2Token implementation = new TestL2Token();
        console.log("Implementation deployed at:", address(implementation));
        
        // Prepare initialization data
        bytes memory initData = abi.encodeWithSignature(
            "initialize(string,string,uint8,address,address)",
            "Test POC Token",           // name
            "POC",                      // symbol
            18,                         // decimals
            L2_CUSTOM_ERC20_GATEWAY,   // gateway
            address(0)                  // counterpart (will be set later)
        );
        
        // Deploy proxy
        ERC1967Proxy proxy = new ERC1967Proxy(address(implementation), initData);
        TestL2Token token = TestL2Token(address(proxy));
        
        console.log("TestL2Token proxy deployed at:", address(token));
        console.log("Token name:", token.name());
        console.log("Token symbol:", token.symbol());
        console.log("Token decimals:", token.decimals());
        console.log("Token gateway:", token.gateway());
        
        vm.stopBroadcast();
        
        // Save deployment info
        console.log("\n=== DEPLOYMENT COMPLETE ===");
        console.log("Network: Scroll Sepolia");
        console.log("TestL2Token:", address(token));
        console.log("L2CustomERC20Gateway:", L2_CUSTOM_ERC20_GATEWAY);
        console.log("\nNext steps:");
        console.log("1. Note the token address for attack scripts");
        console.log("2. Execute token mapping hijack from L1");
        console.log("3. Execute unlimited minting attack");
        console.log("4. Monitor L2 transactions for _from parameter");
    }
}
