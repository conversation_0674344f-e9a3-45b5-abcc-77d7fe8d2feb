# Scroll Contracts Vulnerability Assessment

## Vulnerability Overview

A critical vulnerability has been identified in the Scroll bridge contracts that could allow an attacker to execute arbitrary calls to sensitive contracts from L1 to L2. This vulnerability stems from insufficient validation in the `_validateTargetAddress()` function, which only prevents calls to the messenger contract itself but fails to protect other critical infrastructure contracts.

## Root Cause Analysis

### Primary Vulnerability
**File**: `src/L2/L2ScrollMessenger.sol`
**Function**: `_executeMessage()`
**Lines**: 143-171

```solidity
function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    // @note check more `_to` address to avoid attack in the future when we add more gateways.
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);  // ← INSUFFICIENT PROTECTION

    // @note This usually will never happen, just in case.
    require(_from != xDomainMessageSender, "Invalid message sender");

    xDomainMessageSender = _from;  // ← ATTACKER CONTROLS THIS
    // solhint-disable-next-line avoid-low-level-calls
    // no reentrancy risk, only alias(l1ScrollMessenger) can call relayMessage.
    // slither-disable-next-line reentrancy-eth
    (bool success, ) = _to.call{value: _value}(_message);  // ← ARBITRARY CALL EXECUTION
    // reset value to refund gas.
    xDomainMessageSender = ScrollConstants.DEFAULT_XDOMAIN_MESSAGE_SENDER;

    if (success) {
        isL1MessageExecuted[_xDomainCalldataHash] = true;
        emit RelayedMessage(_xDomainCalldataHash);
    } else {
        emit FailedRelayedMessage(_xDomainCalldataHash);
    }
}
```

### Secondary Vulnerability
**File**: `src/libraries/ScrollMessengerBase.sol`
**Function**: `_validateTargetAddress()`
**Lines**: 149-155

```solidity
function _validateTargetAddress(address _target) internal view {
    // @note check more `_target` address to avoid attack in the future when we add more external contracts.
    require(_target != address(this), "Forbid to call self");  // ← ONLY BLOCKS SELF-CALLS
}
```

## Technical Analysis

### Insufficient Validation Logic

The current target validation only prevents two scenarios:
- Calls to `messageQueue` contract (checked in `L2ScrollMessenger._executeMessage()`)
- Calls to the messenger contract itself (checked in `ScrollMessengerBase._validateTargetAddress()`)

**Critical Gap**: The validation **DOES NOT** protect critical infrastructure contracts including:
- L2ETHGateway
- L2StandardERC20Gateway
- L2CustomERC20Gateway
- L2WETHGateway
- L2ERC721Gateway
- L2GatewayRouter
- L2USDCGateway
- Administrative contracts

## Vulnerable Contracts and Attack Vectors

### 1. L2ETHGateway

**Risk**: An attacker could directly call this gateway from L1 with arbitrary parameters, potentially:
- Triggering unauthorized ETH withdrawals
- Manipulating the `_from` parameter to impersonate legitimate users
- Bypassing the `onlyCallByCounterpart` modifier by setting `xDomainMessageSender` to the counterpart address

**Key Functions at Risk**:
- `finalizeDepositETH()` - Could be called with arbitrary parameters, potentially leading to unauthorized ETH transfers

### 2. L2GatewayRouter

**Risk**: An attacker could call administrative functions directly from L1, potentially:
- Changing gateway mappings via `setERC20Gateway()`
- Changing the default ERC20 gateway via `setDefaultERC20Gateway()`
- Changing the ETH gateway via `setETHGateway()`

**Key Functions at Risk**:
- `setETHGateway()` - Could redirect ETH withdrawals to a malicious contract
- `setDefaultERC20Gateway()` - Could redirect token withdrawals to a malicious contract
- `setERC20Gateway()` - Could selectively redirect specific token withdrawals

### 3. L2StandardERC20Gateway and L2CustomERC20Gateway

**Risk**: An attacker could directly call these gateways from L1, potentially:
- Triggering unauthorized token withdrawals
- Manipulating the `_from` parameter to impersonate legitimate users
- Bypassing the `onlyCallByCounterpart` modifier by setting `xDomainMessageSender` to the counterpart address

**Key Functions at Risk**:
- `finalizeDepositERC20()` - Could be called with arbitrary parameters, potentially leading to unauthorized token minting or transfers

### 4. L2ERC721Gateway and L2ERC1155Gateway

**Risk**: Similar to ERC20 gateways, an attacker could:
- Trigger unauthorized NFT withdrawals
- Manipulate ownership records
- Bypass access controls

### 5. L2USDCGateway

**Risk**: Particularly concerning due to the high value of USDC assets, an attacker could:
- Trigger unauthorized USDC withdrawals
- Manipulate USDC balances
- Bypass specialized security controls for USDC

### 6. Administrative Contracts

**Risk**: An attacker could call sensitive administrative functions, potentially:
- Changing system parameters
- Updating contract configurations
- Triggering emergency functions

## Attack Scenario

1. Attacker deploys a malicious contract on L1
2. Attacker calls `L1ScrollMessenger.sendMessage()` targeting a critical L2 contract (e.g., L2ETHGateway)
3. When the message is relayed to L2, `L2ScrollMessenger._executeMessage()` performs insufficient validation
4. The `xDomainMessageSender` is set to the attacker's L1 address
5. The arbitrary call is executed with the attacker-controlled parameters
6. Depending on the target contract, this could lead to:
   - Unauthorized asset withdrawals
   - Manipulation of gateway configurations
   - Bypassing of access controls
   - Execution of privileged administrative functions

## Impact Severity

**Critical** - This vulnerability could potentially lead to:
- Theft of all bridged assets (ETH, ERC20, ERC721, ERC1155)
- Permanent disruption of the bridge functionality
- Manipulation of critical system parameters
- Bypass of intended security controls

The vulnerability is particularly severe because:
1. It affects multiple critical contracts
2. It could be exploited in a single transaction
3. It could lead to significant financial losses
4. It undermines the fundamental security model of the cross-domain messaging system
