# Scroll Contracts Security Vulnerability Report

## Executive Summary

A critical security vulnerability has been identified in the Scroll bridge contracts that could allow an attacker to execute arbitrary calls to sensitive contracts from L1 to L2. This vulnerability stems from insufficient validation in the address verification logic, which only prevents calls to the messenger contract itself but fails to protect other critical infrastructure contracts.

The vulnerability affects multiple critical contracts in the Scroll ecosystem, including various gateway contracts responsible for asset transfers between L1 and L2. If exploited, this vulnerability could potentially lead to unauthorized asset withdrawals, manipulation of gateway configurations, bypassing of access controls, and execution of privileged administrative functions.

This report provides a detailed analysis of the vulnerability, its potential impact, and recommended mitigation strategies.

## Vulnerability Details

### Root Cause

**Primary Vulnerability**
**File**: `src/L2/L2ScrollMessenger.sol`
**Function**: `_executeMessage()`
**Lines**: 143-171

```solidity
function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    // @note check more `_to` address to avoid attack in the future when we add more gateways.
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);  // ← INSUFFICIENT PROTECTION

    // @note This usually will never happen, just in case.
    require(_from != xDomainMessageSender, "Invalid message sender");

    xDomainMessageSender = _from;  // ← ATTACKER CONTROLS THIS
    // solhint-disable-next-line avoid-low-level-calls
    // no reentrancy risk, only alias(l1ScrollMessenger) can call relayMessage.
    // slither-disable-next-line reentrancy-eth
    (bool success, ) = _to.call{value: _value}(_message);  // ← ARBITRARY CALL EXECUTION
    // reset value to refund gas.
    xDomainMessageSender = ScrollConstants.DEFAULT_XDOMAIN_MESSAGE_SENDER;

    if (success) {
        isL1MessageExecuted[_xDomainCalldataHash] = true;
        emit RelayedMessage(_xDomainCalldataHash);
    } else {
        emit FailedRelayedMessage(_xDomainCalldataHash);
    }
}
```

**Secondary Vulnerability**
**File**: `src/libraries/ScrollMessengerBase.sol`
**Function**: `_validateTargetAddress()`
**Lines**: 149-155

```solidity
function _validateTargetAddress(address _target) internal view {
    // @note check more `_target` address to avoid attack in the future when we add more external contracts.
    require(_target != address(this), "Forbid to call self");  // ← ONLY BLOCKS SELF-CALLS
}
```

### Technical Analysis

The current target validation only prevents two scenarios:
- Calls to `messageQueue` contract (checked in `L2ScrollMessenger._executeMessage()`)
- Calls to the messenger contract itself (checked in `ScrollMessengerBase._validateTargetAddress()`)

**Critical Gap**: The validation **DOES NOT** protect critical infrastructure contracts including:
- L2ETHGateway
- L2StandardERC20Gateway
- L2CustomERC20Gateway
- L2WETHGateway
- L2ERC721Gateway
- L2GatewayRouter
- L2USDCGateway
- Administrative contracts

The code comments in both functions acknowledge the need for more comprehensive validation:
- In `_executeMessage()`: "check more `_to` address to avoid attack in the future when we add more gateways."
- In `_validateTargetAddress()`: "check more `_target` address to avoid attack in the future when we add more external contracts."

However, the implementation only blocks calls to the messenger itself and the message queue, leaving all other contracts vulnerable.

## Vulnerable Contracts and Attack Vectors

### 1. L2ETHGateway

**Risk**: An attacker could directly call this gateway from L1 with arbitrary parameters, potentially:
- Triggering unauthorized ETH withdrawals
- Manipulating the `_from` parameter to impersonate legitimate users
- Bypassing the `onlyCallByCounterpart` modifier by setting `xDomainMessageSender` to the counterpart address

**Key Functions at Risk**:
- `finalizeDepositETH()` - Could be called with arbitrary parameters, potentially leading to unauthorized ETH transfers

### 2. L2GatewayRouter

**Risk**: An attacker could call administrative functions directly from L1, potentially:
- Changing gateway mappings via `setERC20Gateway()`
- Changing the default ERC20 gateway via `setDefaultERC20Gateway()`
- Changing the ETH gateway via `setETHGateway()`

**Key Functions at Risk**:
- `setETHGateway()` - Could redirect ETH withdrawals to a malicious contract
- `setDefaultERC20Gateway()` - Could redirect token withdrawals to a malicious contract
- `setERC20Gateway()` - Could selectively redirect specific token withdrawals

### 3. L2StandardERC20Gateway and L2CustomERC20Gateway

**Risk**: An attacker could directly call these gateways from L1, potentially:
- Triggering unauthorized token withdrawals
- Manipulating the `_from` parameter to impersonate legitimate users
- Bypassing the `onlyCallByCounterpart` modifier by setting `xDomainMessageSender` to the counterpart address

**Key Functions at Risk**:
- `finalizeDepositERC20()` - Could be called with arbitrary parameters, potentially leading to unauthorized token minting or transfers

### 4. L2ERC721Gateway and L2ERC1155Gateway

**Risk**: Similar to ERC20 gateways, an attacker could:
- Trigger unauthorized NFT withdrawals
- Manipulate ownership records
- Bypass access controls

### 5. L2USDCGateway

**Risk**: Particularly concerning due to the high value of USDC assets, an attacker could:
- Trigger unauthorized USDC withdrawals
- Manipulate USDC balances
- Bypass specialized security controls for USDC

### 6. Administrative Contracts

**Risk**: An attacker could call sensitive administrative functions, potentially:
- Changing system parameters
- Updating contract configurations
- Triggering emergency functions

## Attack Scenario

1. Attacker deploys a malicious contract on L1
2. Attacker calls `L1ScrollMessenger.sendMessage()` targeting a critical L2 contract (e.g., L2ETHGateway)
3. When the message is relayed to L2, `L2ScrollMessenger._executeMessage()` performs insufficient validation
4. The `xDomainMessageSender` is set to the attacker's L1 address
5. The arbitrary call is executed with the attacker-controlled parameters
6. Depending on the target contract, this could lead to:
   - Unauthorized asset withdrawals
   - Manipulation of gateway configurations
   - Bypassing of access controls
   - Execution of privileged administrative functions

## Impact Severity

**Critical** - This vulnerability could potentially lead to:
- Theft of all bridged assets (ETH, ERC20, ERC721, ERC1155)
- Permanent disruption of the bridge functionality
- Manipulation of critical system parameters
- Bypass of intended security controls

The vulnerability is particularly severe because:
1. It affects multiple critical contracts
2. It could be exploited in a single transaction
3. It could lead to significant financial losses
4. It undermines the fundamental security model of the cross-domain messaging system

## Recommended Mitigations

### 1. Implement Comprehensive Address Validation

#### Option A: Explicit Allowlist Approach (Recommended)

```solidity
// Add to ScrollMessengerBase.sol
mapping(address => bool) public allowedTargets;

function _validateTargetAddress(address _target) internal view {
    require(_target != address(this), "Forbid to call self");
    require(allowedTargets[_target], "Target not in allowlist");
}

// Add administrative function to manage allowlist
function setAllowedTarget(address _target, bool _allowed) external onlyOwner {
    allowedTargets[_target] = _allowed;
    emit AllowedTargetUpdated(_target, _allowed);
}
```

**Advantages:**
- Most secure approach - explicit control over allowed targets
- Clear security boundary - only explicitly approved contracts can be called
- Flexible - can be updated as new contracts are added to the system

**Implementation Notes:**
- Initialize with all legitimate targets during deployment
- Requires careful management when adding new contracts
- Should emit events for all allowlist changes for transparency

#### Option B: Explicit Denylist Approach (Alternative)

```solidity
// Add to ScrollMessengerBase.sol
mapping(address => bool) public blockedTargets;

function _validateTargetAddress(address _target) internal view {
    require(_target != address(this), "Forbid to call self");
    require(!blockedTargets[_target], "Target in denylist");
}

// Add administrative function to manage denylist
function setBlockedTarget(address _target, bool _blocked) external onlyOwner {
    blockedTargets[_target] = _blocked;
    emit BlockedTargetUpdated(_target, _blocked);
}
```

**Advantages:**
- More flexible for adding new contracts
- Lower maintenance overhead

**Disadvantages:**
- Less secure than allowlist - new vulnerable contracts might be missed
- Requires comprehensive initial setup to block all sensitive contracts

### 2. Enhanced Gateway Protection

Add additional validation in gateway contracts to verify the caller context:

```solidity
// Add to all gateway contracts that implement finalizeDeposit functions
modifier onlyFromL1Counterpart() {
    require(
        msg.sender == address(messenger) &&
        IScrollMessenger(messenger).xDomainMessageSender() == counterpart,
        "Only callable from L1 counterpart"
    );
    _;
}
```

Apply this modifier to all sensitive functions in gateway contracts:

```solidity
function finalizeDepositETH(
    address _from,
    address _to,
    uint256 _amount,
    bytes calldata _data
) external payable override onlyFromL1Counterpart nonReentrant {
    // Existing implementation
}
```

### 3. Role-Based Access Control for Administrative Functions

Implement OpenZeppelin's AccessControl for administrative functions:

```solidity
// Add to contracts with administrative functions
import "@openzeppelin/contracts/access/AccessControl.sol";

// Define roles
bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");

// Apply role checks to sensitive functions
function setETHGateway(address _newEthGateway) external onlyRole(ADMIN_ROLE) {
    // Existing implementation
}
```

### 4. Message Source Validation

Enhance the message relay process to validate the source of messages:

```solidity
// Add to L2ScrollMessenger.sol
mapping(address => bool) public trustedSenders;

function relayMessage(
    address _from,
    address _to,
    uint256 _value,
    uint256 _nonce,
    bytes memory _message
) external override whenNotPaused {
    // Existing checks
    require(AddressAliasHelper.undoL1ToL2Alias(_msgSender()) == counterpart, "Caller is not L1ScrollMessenger");
    
    // Add sender validation
    require(trustedSenders[_from], "Untrusted L1 sender");
    
    // Existing implementation
}
```

### 5. Function Selector Validation

Add validation for allowed function selectors when calling specific contracts:

```solidity
// Add to L2ScrollMessenger.sol
mapping(address => mapping(bytes4 => bool)) public allowedSelectors;

function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    // Existing validation
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);
    
    // Add function selector validation
    if (_message.length >= 4) {
        bytes4 selector;
        assembly {
            selector := mload(add(_message, 32))
        }
        require(allowedSelectors[_to][selector], "Function selector not allowed for target");
    }
    
    // Existing implementation
}
```

## Implementation Priority

1. **Immediate (Critical)**: Implement the allowlist approach for target validation
2. **High**: Add enhanced gateway protection with explicit caller validation
3. **High**: Implement function selector validation for sensitive contracts
4. **Medium**: Add role-based access control for administrative functions
5. **Medium**: Implement message source validation
6. **Low**: Add emergency circuit breaker functionality

## Conclusion

The identified vulnerability in the Scroll bridge contracts represents a critical security risk that could potentially compromise the entire bridge system and lead to significant financial losses. The vulnerability stems from insufficient validation in the address verification logic, which allows arbitrary calls to sensitive contracts.

Immediate action is recommended to implement the proposed mitigation strategies, with priority given to the allowlist approach for target validation. Additionally, a comprehensive security audit of all contracts in the Scroll ecosystem is advised to identify any other potential vulnerabilities.

By implementing these mitigations, the security of the Scroll bridge can be significantly enhanced, protecting users' assets and maintaining the integrity of the cross-domain messaging system.
