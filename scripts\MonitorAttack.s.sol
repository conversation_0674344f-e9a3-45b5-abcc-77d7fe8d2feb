// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Script, console} from "forge-std/Script.sol";
import {TestL2Token} from "../src/testnet/TestL2Token.sol";

/**
 * @title MonitorAttack
 * @notice Monitor the results of the vulnerability POC on Scroll Sepolia
 */
contract MonitorAttack is Script {

    // Scroll Sepolia addresses
    address constant L2_CUSTOM_ERC20_GATEWAY = 0x9aD3c5617eCAa556d6E166787A97081907171230;

    // These will be set based on your deployments
    address constant L1_ATTACKER_CONTRACT = 0x339d94CD81c87D6Ef362A824039661ce1d2De054; // ✅ DEPLOYED ON L1
    address constant L2_TEST_TOKEN = 0x7DB1015435D34Ae12FF5a42FE4b6c429734617FD;        // ✅ DEPLOYED ON L2

    function run() external view {
        require(L1_ATTACKER_CONTRACT != address(0), "Set L1_ATTACKER_CONTRACT address");
        require(L2_TEST_TOKEN != address(0), "Set L2_TEST_TOKEN address");

        console.log("=== MONITORING ATTACK RESULTS ===");
        console.log("L1 Attacker Contract:", L1_ATTACKER_CONTRACT);
        console.log("L2 Test Token:", L2_TEST_TOKEN);
        console.log("L2 Gateway:", L2_CUSTOM_ERC20_GATEWAY);

        TestL2Token token = TestL2Token(L2_TEST_TOKEN);

        // Check token state
        console.log("\n=== TOKEN STATE ===");
        console.log("Token Name:", token.name());
        console.log("Token Symbol:", token.symbol());
        console.log("Total Supply:", token.totalSupply());
        console.log("Gateway:", token.gateway());
        console.log("Counterpart:", token.counterpart());

        // Check if mapping was hijacked
        console.log("\n=== MAPPING HIJACK CHECK ===");
        if (token.counterpart() == L1_ATTACKER_CONTRACT) {
            console.log("SUCCESS: Token mapping hijacked!");
            console.log("Counterpart now points to attacker contract");
        } else {
            console.log("Token mapping not yet hijacked");
            console.log("Current counterpart:", token.counterpart());
        }

        // Check deployer balance (recipient of minted tokens)
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        uint256 deployerBalance = token.balanceOf(deployer);

        console.log("\n=== MINTING CHECK ===");
        console.log("Deployer address:", deployer);
        console.log("Deployer token balance:", deployerBalance);

        if (deployerBalance > 0) {
            console.log("SUCCESS: Unlimited minting worked!");
            console.log("Tokens minted:", deployerBalance);
        } else {
            console.log("No tokens minted yet (or attack failed)");
        }

        console.log("\n=== VULNERABILITY CONFIRMATION ===");
        if (token.counterpart() == L1_ATTACKER_CONTRACT && deployerBalance > 0) {
            console.log("VULNERABILITY CONFIRMED!");
            console.log("- Token mapping successfully hijacked");
            console.log("- Unlimited tokens successfully minted");
            console.log("- _from parameter was user-controlled");
            console.log("- Company's response is incorrect");
        } else {
            console.log("Attack still in progress or failed");
            console.log("Check L2 transactions for processing status");
        }

        console.log("\n=== NEXT STEPS ===");
        console.log("1. Check Scroll Sepolia explorer for L2 transactions");
        console.log("2. Look for L2ScrollMessenger.relayMessage() calls");
        console.log("3. Verify _from parameter equals:", L1_ATTACKER_CONTRACT);
        console.log("4. Confirm xDomainMessageSender is set to attacker address");
        console.log("5. Document the complete attack flow");
    }

    /**
     * @notice Check specific address balance
     */
    function checkBalance(address account) external view {
        require(L2_TEST_TOKEN != address(0), "Set L2_TEST_TOKEN address");

        TestL2Token token = TestL2Token(L2_TEST_TOKEN);
        uint256 balance = token.balanceOf(account);

        console.log("Account:", account);
        console.log("Token balance:", balance);
    }
}
